// 🧪 Test QR Security Edge Function
// This script tests the deployed edge function

const PROJECT_REF = 'wclwxrilybnzkhvqzbmy';
const FUNCTION_URL = `https://${PROJECT_REF}.supabase.co/functions/v1/qr-security`;

// Test data
const testGenerateRequest = {
  action: 'generate',
  room_id: 'test-room-id',
  school_id: 'test-school-id',
  block_id: 'test-block-id',
  user_id: 'test-user-id',
  expiry_seconds: 300
};

const testValidateRequest = {
  action: 'validate',
  qr_data: {
    room_id: 'test-room-id',
    session_id: 'test-session-id',
    timestamp: new Date().toISOString(),
    expires_at: new Date(Date.now() + 300000).toISOString(),
    school_id: 'test-school-id',
    block_id: 'test-block-id',
    nonce: 'test-nonce',
    challenge: 'test-challenge',
    signature: 'test-signature'
  },
  student_id: 'test-student-id'
};

async function testFunction() {
  console.log('🧪 Testing QR Security Edge Function');
  console.log(`📍 Function URL: ${FUNCTION_URL}`);
  console.log('');

  // Test 1: CORS preflight
  console.log('1️⃣ Testing CORS (OPTIONS request)...');
  try {
    const corsResponse = await fetch(FUNCTION_URL, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'content-type,authorization'
      }
    });
    
    console.log(`   Status: ${corsResponse.status}`);
    console.log(`   CORS Headers: ${corsResponse.headers.get('Access-Control-Allow-Origin')}`);
    
    if (corsResponse.status === 200) {
      console.log('   ✅ CORS test passed');
    } else {
      console.log('   ❌ CORS test failed');
    }
  } catch (error) {
    console.log(`   ❌ CORS test error: ${error.message}`);
  }
  
  console.log('');

  // Test 2: Generate QR Code (without auth - should fail gracefully)
  console.log('2️⃣ Testing QR Generation (without auth)...');
  try {
    const generateResponse = await fetch(FUNCTION_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testGenerateRequest)
    });
    
    const generateResult = await generateResponse.json();
    console.log(`   Status: ${generateResponse.status}`);
    console.log(`   Response:`, generateResult);
    
    if (generateResponse.status === 401) {
      console.log('   ✅ Auth validation working (expected 401)');
    } else {
      console.log('   ⚠️  Unexpected response');
    }
  } catch (error) {
    console.log(`   ❌ Generate test error: ${error.message}`);
  }
  
  console.log('');

  // Test 3: Validate QR Code (without auth - should fail gracefully)
  console.log('3️⃣ Testing QR Validation (without auth)...');
  try {
    const validateResponse = await fetch(FUNCTION_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testValidateRequest)
    });
    
    const validateResult = await validateResponse.json();
    console.log(`   Status: ${validateResponse.status}`);
    console.log(`   Response:`, validateResult);
    
    if (validateResponse.status === 401) {
      console.log('   ✅ Auth validation working (expected 401)');
    } else {
      console.log('   ⚠️  Unexpected response');
    }
  } catch (error) {
    console.log(`   ❌ Validate test error: ${error.message}`);
  }
  
  console.log('');

  // Test 4: Invalid action
  console.log('4️⃣ Testing invalid action...');
  try {
    const invalidResponse = await fetch(FUNCTION_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ action: 'invalid' })
    });
    
    const invalidResult = await invalidResponse.json();
    console.log(`   Status: ${invalidResponse.status}`);
    console.log(`   Response:`, invalidResult);
    
    if (invalidResponse.status === 401) {
      console.log('   ✅ Auth validation working (expected 401)');
    } else if (invalidResponse.status === 400) {
      console.log('   ✅ Input validation working (expected 400)');
    } else {
      console.log('   ⚠️  Unexpected response');
    }
  } catch (error) {
    console.log(`   ❌ Invalid action test error: ${error.message}`);
  }
  
  console.log('');

  // Test 5: Invalid JSON
  console.log('5️⃣ Testing invalid JSON...');
  try {
    const jsonResponse = await fetch(FUNCTION_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: 'invalid json'
    });
    
    const jsonResult = await jsonResponse.json();
    console.log(`   Status: ${jsonResponse.status}`);
    console.log(`   Response:`, jsonResult);
    
    if (jsonResponse.status === 400) {
      console.log('   ✅ JSON validation working (expected 400)');
    } else {
      console.log('   ⚠️  Unexpected response');
    }
  } catch (error) {
    console.log(`   ❌ JSON test error: ${error.message}`);
  }

  console.log('');
  console.log('🎉 Edge function testing completed!');
  console.log('');
  console.log('📋 Next steps:');
  console.log('1. Set the SUPABASE_SERVICE_ROLE_KEY environment variable');
  console.log('2. Test with proper authentication from your app');
  console.log('3. Monitor function logs: supabase functions logs qr-security');
  console.log('');
  console.log('🔍 Function Dashboard:');
  console.log(`   https://supabase.com/dashboard/project/${PROJECT_REF}/functions/qr-security`);
}

// Run the test
testFunction().catch(console.error);
