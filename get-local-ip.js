#!/usr/bin/env node

import os from "os";

function getLocalIP() {
  const interfaces = os.networkInterfaces();
  const addresses = [];

  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === "IPv4" && !iface.internal) {
        addresses.push({
          name: name,
          address: iface.address,
        });
      }
    }
  }

  return addresses;
}

function main() {
  console.log("🌐 Local Network Addresses for Mobile Testing:\n");

  const addresses = getLocalIP();

  if (addresses.length === 0) {
    console.log("❌ No network interfaces found");
    console.log("📋 You can still use: https://localhost:5173");
    return;
  }

  addresses.forEach((addr, index) => {
    console.log(`${index + 1}. ${addr.name}: https://${addr.address}:5173`);
  });

  console.log("\n📱 To test on your phone:");
  console.log("1. Make sure your phone is on the same WiFi network");
  console.log("2. Open any of the above URLs in your phone browser");
  console.log("3. Accept the security certificate warning");
  console.log("4. Test biometric authentication!");

  console.log(
    "\n🔐 WebAuthn/Biometric authentication requires HTTPS to work on mobile devices."
  );
}

main();
