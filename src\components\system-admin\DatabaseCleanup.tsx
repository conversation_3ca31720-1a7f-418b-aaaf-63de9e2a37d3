import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import { format, formatDistanceToNow } from "date-fns";
import {
  Trash2,
  RefreshCw,
  Calendar,
  Clock,
  AlertTriangle,
  Info,
  Database,
  BarChart,
  CheckCircle,
  XCircle,
  Check,
  History,
  Bell,
  FileText,
  Activity,
  MessageSquare,
  MapPin,
  Shield,
  Monitor,
  QrCode,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import {
  getDatabaseCleanupSettings,
  updateDatabaseCleanupSettings,
  triggerDatabaseCleanup,
  getCleanupStatistics,
  DatabaseCleanupSettings,
  CleanupResults,
} from "@/lib/services/database-cleanup-service";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

export default function DatabaseCleanup() {
  const { toast } = useToast();
  const { profile } = useAuth();
  const [settings, setSettings] = useState<DatabaseCleanupSettings | null>(
    null
  );
  const [statistics, setStatistics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [cleanupRunning, setCleanupRunning] = useState(false);
  const [cleanupResults, setCleanupResults] = useState<CleanupResults | null>(
    null
  );
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  // Fetch settings and statistics on component mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // First check if the database_cleanup_settings table exists
        // by running the migration if needed
        try {
          // Import dynamically to avoid circular dependencies
          const { runDatabaseCleanupMigration } = await import(
            "@/lib/migrations/run-database-cleanup-migration"
          );
          await runDatabaseCleanupMigration();
        } catch (migrationError) {
          console.log(
            "Note: Migration may have already been run:",
            migrationError
          );
          // Continue even if migration fails, as it might already exist
        }

        // Now fetch the settings and statistics
        const [settingsData, statsData] = await Promise.all([
          getDatabaseCleanupSettings(),
          getCleanupStatistics(),
        ]);

        if (settingsData) {
          setSettings(settingsData);
        } else {
          // If settings are null, use default settings
          toast({
            title: "Using Default Settings",
            description:
              "Could not load saved settings. Using default configuration.",
            variant: "default",
          });
        }

        if (statsData) {
          setStatistics(statsData);
        } else {
          // If statistics are null, use empty statistics
          toast({
            title: "Statistics Unavailable",
            description:
              "Could not load database statistics. Some information may be missing.",
            variant: "default",
          });
        }
      } catch (error) {
        console.error("Error fetching database cleanup data:", error);
        toast({
          title: "Error",
          description:
            "Failed to fetch database cleanup settings. Using default configuration.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  // Function to handle data type selection
  const handleDataTypeSelection = (dataType: string, checked: boolean) => {
    if (!settings) return;

    // Ensure selected_data_types is an array
    const currentTypes = Array.isArray(settings.selected_data_types)
      ? [...settings.selected_data_types]
      : [];

    if (checked && !currentTypes.includes(dataType)) {
      // Add the data type
      currentTypes.push(dataType);
    } else if (!checked && currentTypes.includes(dataType)) {
      // Remove the data type
      const index = currentTypes.indexOf(dataType);
      currentTypes.splice(index, 1);
    }

    setSettings({
      ...settings,
      selected_data_types: currentTypes,
    });
  };

  // Check if a data type is selected
  const isDataTypeSelected = (dataType: string): boolean => {
    if (!settings) return false;

    // Ensure selected_data_types is an array
    const selectedTypes = Array.isArray(settings.selected_data_types)
      ? settings.selected_data_types
      : [];

    return selectedTypes.includes(dataType);
  };

  // Handle settings update
  const handleSaveSettings = async () => {
    if (!settings || !profile) return;

    setSaving(true);
    try {
      const success = await updateDatabaseCleanupSettings(settings, profile.id);

      if (success) {
        toast({
          title: "Settings Saved",
          description:
            "Database cleanup settings have been updated successfully.",
          variant: "success",
        });

        // Refresh statistics
        const statsData = await getCleanupStatistics();
        setStatistics(statsData);
      } else {
        toast({
          title: "Error",
          description: "Failed to update database cleanup settings.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error saving database cleanup settings:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred while saving settings.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Show confirmation dialog before cleanup
  const handleTriggerCleanup = () => {
    if (!settings) return;
    setConfirmDialogOpen(true);
  };

  // Perform the actual cleanup after confirmation
  const performCleanup = async () => {
    if (!settings) return;

    setCleanupRunning(true);
    setConfirmDialogOpen(false);

    try {
      // Use force=true to clean up all records regardless of retention settings
      const results = await triggerDatabaseCleanup(true);

      if (results) {
        setCleanupResults(results);

        // Refresh settings and statistics
        const [settingsData, statsData] = await Promise.all([
          getDatabaseCleanupSettings(),
          getCleanupStatistics(),
        ]);

        setSettings(settingsData);
        setStatistics(statsData);

        // Calculate total records removed
        const totalRecordsRemoved = results.total_deleted || (
          results.notifications_deleted +
          results.attendance_records_deleted +
          results.audit_logs_deleted +
          results.excuses_deleted +
          (results.location_alerts_deleted || 0) +
          (results.biometric_credentials_deleted || 0) +
          (results.feedback_submissions_deleted || 0) +
          (results.system_logs_deleted || 0) +
          (results.user_activity_logs_deleted || 0) +
          (results.qr_sessions_deleted || 0)
        );

        toast({
          title: "Cleanup Successful",
          description: `Database cleanup completed successfully. ${totalRecordsRemoved} records removed.`,
          variant: "success",
        });
      } else {
        setCleanupResults(null);
        toast({
          title: "Cleanup Failed",
          description:
            "Failed to trigger database cleanup. Please check if the database is accessible.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error triggering database cleanup:", error);
      setCleanupResults(null);
      toast({
        title: "Cleanup Error",
        description:
          "An unexpected error occurred during cleanup. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setCleanupRunning(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Database Cleanup</CardTitle>
          <CardDescription>
            Loading database cleanup settings...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      {/* Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-red-600">
              Confirm Database Cleanup
            </DialogTitle>
            <DialogDescription>
              You are about to permanently delete data from the database. This
              action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <h3 className="font-medium mb-2">
              The following data will be deleted:
            </h3>
            <div className="space-y-2 max-h-60 overflow-y-auto border rounded-md p-3">
              {settings?.selected_data_types?.includes("notifications") && (
                <div className="flex items-center">
                  <Bell className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>Notifications</span>
                  <span className="ml-auto font-medium">
                    {statistics?.current_counts?.notifications || 0} records
                  </span>
                </div>
              )}
              {settings?.selected_data_types?.includes(
                "attendance_records"
              ) && (
                <div className="flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>Attendance Records</span>
                  <span className="ml-auto font-medium">
                    {statistics?.current_counts?.attendance_records || 0}{" "}
                    records
                  </span>
                </div>
              )}
              {settings?.selected_data_types?.includes("audit_logs") && (
                <div className="flex items-center">
                  <Activity className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>Audit Logs</span>
                  <span className="ml-auto font-medium">
                    {statistics?.current_counts?.audit_logs || 0} records
                  </span>
                </div>
              )}
              {settings?.selected_data_types?.includes("excuses") && (
                <div className="flex items-center">
                  <MessageSquare className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>Excuses</span>
                  <span className="ml-auto font-medium">
                    {statistics?.current_counts?.excuses || 0} records
                  </span>
                </div>
              )}
              {settings?.selected_data_types?.includes("alerts") && (
                <div className="flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>Alerts</span>
                  <span className="ml-auto font-medium">
                    {statistics?.current_counts?.alerts || 0} records
                  </span>
                </div>
              )}
              {settings?.selected_data_types?.includes("history") && (
                <div className="flex items-center">
                  <History className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>History</span>
                  <span className="ml-auto font-medium">
                    {statistics?.current_counts?.history || 0} records
                  </span>
                </div>
              )}
              {settings?.selected_data_types?.includes(
                "user_activity_logs"
              ) && (
                <div className="flex items-center">
                  <Activity className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>User Activity Logs</span>
                  <span className="ml-auto font-medium">
                    {statistics?.current_counts?.user_activity_logs || 0}{" "}
                    records
                  </span>
                </div>
              )}
              {settings?.selected_data_types?.includes("notification_logs") && (
                <div className="flex items-center">
                  <Bell className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>Notification Logs</span>
                  <span className="ml-auto font-medium">
                    {statistics?.current_counts?.notification_logs || 0} records
                  </span>
                </div>
              )}
            </div>

            <div className="mt-4 text-sm text-muted-foreground">
              <AlertTriangle className="h-4 w-4 inline-block mr-1 text-red-500" />
              This action will delete all selected data types regardless of
              retention settings.
            </div>
          </div>

          <DialogFooter className="sm:justify-between">
            <Button
              variant="outline"
              onClick={() => setConfirmDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={performCleanup}
              disabled={cleanupRunning}
            >
              {cleanupRunning ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Cleaning...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Confirm Cleanup
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <CardHeader>
        <div className="flex items-center gap-2">
          <Database className="h-5 w-5 text-primary" />
          <CardTitle>Database Cleanup Automation</CardTitle>
        </div>
        <CardDescription>
          Configure automatic cleanup of non-essential data to optimize database
          performance
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Information Alert */}
        <Alert className="bg-blue-50 border-blue-200">
          <Info className="h-4 w-4 text-blue-600" />
          <AlertTitle className="text-blue-800">
            About Database Cleanup
          </AlertTitle>
          <AlertDescription className="text-blue-700">
            Database cleanup helps maintain system performance by removing old,
            non-essential data. Important user data is preserved while temporary
            data like notifications, old attendance records, and audit logs are
            removed based on your retention settings.
          </AlertDescription>
        </Alert>

        {/* Enable/Disable Switch */}
        <div className="flex items-center justify-between p-4 border rounded-md">
          <div>
            <h3 className="text-lg font-medium">Automatic Database Cleanup</h3>
            <p className="text-sm text-muted-foreground">
              Enable scheduled cleanup of non-essential data
            </p>
          </div>
          <Switch
            checked={settings?.enabled || false}
            onCheckedChange={(checked) =>
              setSettings(settings ? { ...settings, enabled: checked } : null)
            }
          />
        </div>

        {/* Data Type Selection */}
        <div className="space-y-4 p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">Data Types to Clean Up</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Select which types of data should be included in the automatic
            cleanup
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="notifications-type"
                checked={isDataTypeSelected("notifications")}
                onCheckedChange={(checked) =>
                  handleDataTypeSelection("notifications", checked === true)
                }
              />
              <Label htmlFor="notifications-type" className="flex items-center">
                <Bell className="h-4 w-4 mr-2 text-muted-foreground" />
                Notifications
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        System notifications sent to users, including attendance
                        alerts and system messages.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="attendance-type"
                checked={isDataTypeSelected("attendance_records")}
                onCheckedChange={(checked) =>
                  handleDataTypeSelection(
                    "attendance_records",
                    checked === true
                  )
                }
              />
              <Label htmlFor="attendance-type" className="flex items-center">
                <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                Attendance Records
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Records of student attendance, including present,
                        absent, and late statuses.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="audit-logs-type"
                checked={isDataTypeSelected("audit_logs")}
                onCheckedChange={(checked) =>
                  handleDataTypeSelection("audit_logs", checked === true)
                }
              />
              <Label htmlFor="audit-logs-type" className="flex items-center">
                <Activity className="h-4 w-4 mr-2 text-muted-foreground" />
                Audit Logs
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        System logs that track administrative actions and
                        important system events.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="excuses-type"
                checked={isDataTypeSelected("excuses")}
                onCheckedChange={(checked) =>
                  handleDataTypeSelection("excuses", checked === true)
                }
              />
              <Label htmlFor="excuses-type" className="flex items-center">
                <MessageSquare className="h-4 w-4 mr-2 text-muted-foreground" />
                Excuses
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Absence excuse records submitted by students and
                        approved/rejected by teachers.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="alerts-type"
                checked={isDataTypeSelected("alerts")}
                onCheckedChange={(checked) =>
                  handleDataTypeSelection("alerts", checked === true)
                }
              />
              <Label htmlFor="alerts-type" className="flex items-center">
                <AlertTriangle className="h-4 w-4 mr-2 text-muted-foreground" />
                Alerts
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        System alerts about attendance issues and other
                        important notifications.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="history-type"
                checked={isDataTypeSelected("history")}
                onCheckedChange={(checked) =>
                  handleDataTypeSelection("history", checked === true)
                }
              />
              <Label htmlFor="history-type" className="flex items-center">
                <History className="h-4 w-4 mr-2 text-muted-foreground" />
                History
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Historical records of student activities and system
                        events shown in the history tab.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="activity-logs-type"
                checked={isDataTypeSelected("user_activity_logs")}
                onCheckedChange={(checked) =>
                  handleDataTypeSelection(
                    "user_activity_logs",
                    checked === true
                  )
                }
              />
              <Label htmlFor="activity-logs-type" className="flex items-center">
                <Activity className="h-4 w-4 mr-2 text-muted-foreground" />
                User Activity Logs
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Logs of user activities such as logins, logouts, and
                        other user actions.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="notification-logs-type"
                checked={isDataTypeSelected("notification_logs")}
                onCheckedChange={(checked) =>
                  handleDataTypeSelection("notification_logs", checked === true)
                }
              />
              <Label
                htmlFor="notification-logs-type"
                className="flex items-center"
              >
                <Bell className="h-4 w-4 mr-2 text-muted-foreground" />
                Notification Logs
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Logs of notification deliveries and read receipts.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="location-alerts-type"
                checked={isDataTypeSelected("location_alerts")}
                onCheckedChange={(checked) =>
                  handleDataTypeSelection("location_alerts", checked === true)
                }
              />
              <Label htmlFor="location-alerts-type" className="flex items-center">
                <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                Location Alerts
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Alerts generated when students check in from unexpected locations.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="biometric-credentials-type"
                checked={isDataTypeSelected("biometric_credentials")}
                onCheckedChange={(checked) =>
                  handleDataTypeSelection("biometric_credentials", checked === true)
                }
              />
              <Label htmlFor="biometric-credentials-type" className="flex items-center">
                <Shield className="h-4 w-4 mr-2 text-muted-foreground" />
                Biometric Credentials
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Stored biometric data used for student authentication.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="system-logs-type"
                checked={isDataTypeSelected("system_logs")}
                onCheckedChange={(checked) =>
                  handleDataTypeSelection("system_logs", checked === true)
                }
              />
              <Label htmlFor="system-logs-type" className="flex items-center">
                <Monitor className="h-4 w-4 mr-2 text-muted-foreground" />
                System Logs
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        System-level logs including errors, warnings, and debug information.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="feedback-submissions-type"
                checked={isDataTypeSelected("feedback_submissions")}
                onCheckedChange={(checked) =>
                  handleDataTypeSelection("feedback_submissions", checked === true)
                }
              />
              <Label htmlFor="feedback-submissions-type" className="flex items-center">
                <MessageSquare className="h-4 w-4 mr-2 text-muted-foreground" />
                Feedback Submissions
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        User feedback and bug reports submitted through the system.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="qr-sessions-type"
                checked={isDataTypeSelected("qr_sessions")}
                onCheckedChange={(checked) =>
                  handleDataTypeSelection("qr_sessions", checked === true)
                }
              />
              <Label htmlFor="qr-sessions-type" className="flex items-center">
                <QrCode className="h-4 w-4 mr-2 text-muted-foreground" />
                QR Sessions
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        QR code session data and expired QR code records.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
            </div>
          </div>
        </div>

        {/* Retention Settings */}
        <div className="space-y-4 p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">Data Retention Settings</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Configure how long to keep different types of data before automatic
            cleanup
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="notifications-retention">
                Notifications Retention (days)
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Notifications older than this many days will be
                        permanently deleted. These include attendance alerts,
                        system notifications, and other messages.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <Input
                id="notifications-retention"
                type="number"
                min="1"
                value={settings?.notifications_retention_days || 90}
                onChange={(e) =>
                  setSettings(
                    settings
                      ? {
                          ...settings,
                          notifications_retention_days:
                            parseInt(e.target.value) || 90,
                        }
                      : null
                  )
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="attendance-retention">
                Attendance Records Retention (days)
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Attendance records older than this many days will be
                        permanently deleted. Consider academic terms when
                        setting this value.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <Input
                id="attendance-retention"
                type="number"
                min="1"
                value={settings?.attendance_records_retention_days || 365}
                onChange={(e) =>
                  setSettings(
                    settings
                      ? {
                          ...settings,
                          attendance_records_retention_days:
                            parseInt(e.target.value) || 365,
                        }
                      : null
                  )
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="audit-logs-retention">
                Audit Logs Retention (days)
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        System audit logs older than this many days will be
                        permanently deleted. These logs track administrative
                        actions and system events.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <Input
                id="audit-logs-retention"
                type="number"
                min="1"
                value={settings?.audit_logs_retention_days || 180}
                onChange={(e) =>
                  setSettings(
                    settings
                      ? {
                          ...settings,
                          audit_logs_retention_days:
                            parseInt(e.target.value) || 180,
                        }
                      : null
                  )
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="excuses-retention">
                Excuses Retention (days)
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Absence excuse records older than this many days will be
                        permanently deleted.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <Input
                id="excuses-retention"
                type="number"
                min="1"
                value={settings?.excuses_retention_days || 180}
                onChange={(e) =>
                  setSettings(
                    settings
                      ? {
                          ...settings,
                          excuses_retention_days:
                            parseInt(e.target.value) || 180,
                        }
                      : null
                  )
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="alerts-retention">
                Alerts Retention (days)
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        System alerts older than this many days will be
                        permanently deleted.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <Input
                id="alerts-retention"
                type="number"
                min="1"
                value={settings?.alerts_retention_days || 90}
                onChange={(e) =>
                  setSettings(
                    settings
                      ? {
                          ...settings,
                          alerts_retention_days: parseInt(e.target.value) || 90,
                        }
                      : null
                  )
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="history-retention">
                History Retention (days)
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Historical records older than this many days will be
                        permanently deleted.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <Input
                id="history-retention"
                type="number"
                min="1"
                value={settings?.history_retention_days || 180}
                onChange={(e) =>
                  setSettings(
                    settings
                      ? {
                          ...settings,
                          history_retention_days:
                            parseInt(e.target.value) || 180,
                        }
                      : null
                  )
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="activity-logs-retention">
                User Activity Logs Retention (days)
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        User activity logs older than this many days will be
                        permanently deleted.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <Input
                id="activity-logs-retention"
                type="number"
                min="1"
                value={settings?.user_activity_logs_retention_days || 90}
                onChange={(e) =>
                  setSettings(
                    settings
                      ? {
                          ...settings,
                          user_activity_logs_retention_days:
                            parseInt(e.target.value) || 90,
                        }
                      : null
                  )
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notification-logs-retention">
                Notification Logs Retention (days)
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Notification delivery logs older than this many days
                        will be permanently deleted.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <Input
                id="notification-logs-retention"
                type="number"
                min="1"
                value={settings?.notification_logs_retention_days || 90}
                onChange={(e) =>
                  setSettings(
                    settings
                      ? {
                          ...settings,
                          notification_logs_retention_days:
                            parseInt(e.target.value) || 90,
                        }
                      : null
                  )
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="location-alerts-retention">
                Location Alerts Retention (days)
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Location alerts older than this many days will be
                        permanently deleted.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <Input
                id="location-alerts-retention"
                type="number"
                min="1"
                value={settings?.location_alerts_retention_days || 90}
                onChange={(e) =>
                  setSettings(
                    settings
                      ? {
                          ...settings,
                          location_alerts_retention_days:
                            parseInt(e.target.value) || 90,
                        }
                      : null
                  )
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="biometric-credentials-retention">
                Biometric Credentials Retention (days)
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Biometric credentials older than this many days will be
                        permanently deleted.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <Input
                id="biometric-credentials-retention"
                type="number"
                min="1"
                value={settings?.biometric_credentials_retention_days || 180}
                onChange={(e) =>
                  setSettings(
                    settings
                      ? {
                          ...settings,
                          biometric_credentials_retention_days:
                            parseInt(e.target.value) || 180,
                        }
                      : null
                  )
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="system-logs-retention">
                System Logs Retention (days)
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        System logs older than this many days will be
                        permanently deleted.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <Input
                id="system-logs-retention"
                type="number"
                min="1"
                value={settings?.system_logs_retention_days || 90}
                onChange={(e) =>
                  setSettings(
                    settings
                      ? {
                          ...settings,
                          system_logs_retention_days:
                            parseInt(e.target.value) || 90,
                        }
                      : null
                  )
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="feedback-submissions-retention">
                Feedback Submissions Retention (days)
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        Feedback submissions older than this many days will be
                        permanently deleted.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <Input
                id="feedback-submissions-retention"
                type="number"
                min="1"
                value={settings?.feedback_submissions_retention_days || 180}
                onChange={(e) =>
                  setSettings(
                    settings
                      ? {
                          ...settings,
                          feedback_submissions_retention_days:
                            parseInt(e.target.value) || 180,
                        }
                      : null
                  )
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="qr-sessions-retention">
                QR Sessions Retention (days)
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-3.5 w-3.5 ml-1 inline text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-80">
                        QR session data older than this many days will be
                        permanently deleted.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
              <Input
                id="qr-sessions-retention"
                type="number"
                min="1"
                value={settings?.qr_sessions_retention_days || 30}
                onChange={(e) =>
                  setSettings(
                    settings
                      ? {
                          ...settings,
                          qr_sessions_retention_days:
                            parseInt(e.target.value) || 30,
                        }
                      : null
                  )
                }
              />
            </div>
          </div>
        </div>

        {/* Schedule Settings */}
        <div className="p-4 border rounded-md">
          <h3 className="text-lg font-medium mb-2">Cleanup Schedule</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Configure how often the system should run automatic cleanup
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="cleanup-frequency">Cleanup Frequency</Label>
              <Select
                value={settings?.cleanup_frequency || "weekly"}
                onValueChange={(value: "daily" | "weekly" | "monthly") =>
                  setSettings(
                    settings ? { ...settings, cleanup_frequency: value } : null
                  )
                }
              >
                <SelectTrigger id="cleanup-frequency">
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Next Scheduled Cleanup</Label>
              <div className="h-10 px-3 py-2 rounded-md border border-input bg-background text-sm flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                {settings?.next_cleanup_at ? (
                  (() => {
                    try {
                      const nextCleanupDate = new Date(
                        settings.next_cleanup_at
                      );
                      // Check if the date is valid
                      if (isNaN(nextCleanupDate.getTime())) {
                        return (
                          <span className="text-muted-foreground">
                            Not scheduled (invalid date)
                          </span>
                        );
                      }

                      return (
                        <span>
                          {format(nextCleanupDate, "PPP")} (
                          {formatDistanceToNow(nextCleanupDate, {
                            addSuffix: true,
                          })}
                          )
                        </span>
                      );
                    } catch (e) {
                      console.error("Error formatting next cleanup date:", e);
                      return (
                        <span className="text-muted-foreground">
                          Not scheduled (error)
                        </span>
                      );
                    }
                  })()
                ) : (
                  <span className="text-muted-foreground">Not scheduled</span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Database Statistics */}
        {statistics && (
          <div className="p-4 border rounded-md">
            <h3 className="text-lg font-medium mb-2 flex items-center">
              <BarChart className="h-4 w-4 mr-2" />
              Database Statistics
            </h3>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="p-3 bg-muted rounded-md text-center">
                <div className="text-2xl font-bold">
                  {statistics.notifications || 0}
                </div>
                <div className="text-xs text-muted-foreground">
                  Notifications
                </div>
              </div>
              <div className="p-3 bg-muted rounded-md text-center">
                <div className="text-2xl font-bold">
                  {statistics.attendance_records || 0}
                </div>
                <div className="text-xs text-muted-foreground">
                  Attendance Records
                </div>
              </div>
              <div className="p-3 bg-muted rounded-md text-center">
                <div className="text-2xl font-bold">
                  {statistics.audit_logs || 0}
                </div>
                <div className="text-xs text-muted-foreground">Audit Logs</div>
              </div>
              <div className="p-3 bg-muted rounded-md text-center">
                <div className="text-2xl font-bold">
                  {statistics.excuses || 0}
                </div>
                <div className="text-xs text-muted-foreground">Excuses</div>
              </div>
              <div className="p-3 bg-muted rounded-md text-center">
                <div className="text-2xl font-bold">
                  {statistics.location_alerts || 0}
                </div>
                <div className="text-xs text-muted-foreground">Location Alerts</div>
              </div>
              <div className="p-3 bg-muted rounded-md text-center">
                <div className="text-2xl font-bold">
                  {statistics.biometric_credentials || 0}
                </div>
                <div className="text-xs text-muted-foreground">Biometric Credentials</div>
              </div>
              <div className="p-3 bg-muted rounded-md text-center">
                <div className="text-2xl font-bold">
                  {statistics.system_logs || 0}
                </div>
                <div className="text-xs text-muted-foreground">
                  System Logs
                </div>
              </div>
              <div className="p-3 bg-muted rounded-md text-center">
                <div className="text-2xl font-bold">
                  {statistics.user_activity_logs || 0}
                </div>
                <div className="text-xs text-muted-foreground">
                  User Activity Logs
                </div>
              </div>
              <div className="p-3 bg-muted rounded-md text-center">
                <div className="text-2xl font-bold">
                  {statistics.feedback_submissions || 0}
                </div>
                <div className="text-xs text-muted-foreground">
                  Feedback Submissions
                </div>
              </div>
              <div className="p-3 bg-muted rounded-md text-center">
                <div className="text-2xl font-bold">
                  {statistics.qr_sessions || 0}
                </div>
                <div className="text-xs text-muted-foreground">
                  QR Sessions
                </div>
              </div>
              <div className="p-3 bg-muted rounded-md text-center">
                <div className="text-2xl font-bold">
                  {statistics.alerts || statistics.location_alerts || 0}
                </div>
                <div className="text-xs text-muted-foreground">
                  Alerts
                </div>
              </div>
              <div className="p-3 bg-muted rounded-md text-center">
                <div className="text-2xl font-bold">
                  {statistics.history || 0}
                </div>
                <div className="text-xs text-muted-foreground">
                  History
                </div>
              </div>
              <div className="p-3 bg-muted rounded-md text-center">
                <div className="text-2xl font-bold">
                  {statistics.notification_logs || 0}
                </div>
                <div className="text-xs text-muted-foreground">
                  Notification Logs
                </div>
              </div>
            </div>

            {statistics.last_cleanup_at && (
              <div className="text-sm">
                <div className="font-medium">Last Cleanup:</div>
                <div className="text-muted-foreground">
                  {(() => {
                    try {
                      const lastCleanupDate = new Date(statistics.last_cleanup_at);
                      // Check if the date is valid
                      if (isNaN(lastCleanupDate.getTime())) {
                        return "Unknown date";
                      }
                      return (
                        <>
                          {format(lastCleanupDate, "PPP 'at' p")} (
                          {formatDistanceToNow(lastCleanupDate, {
                            addSuffix: true,
                          })}
                          )
                        </>
                      );
                    } catch (e) {
                      console.error("Error formatting last cleanup date:", e);
                      return "Unknown date";
                    }
                  })()}
                </div>
              </div>
            )}

            {statistics.next_cleanup_at && (
              <div className="text-sm mt-2">
                <div className="font-medium">Next Cleanup:</div>
                <div className="text-muted-foreground">
                  {(() => {
                    try {
                      const nextCleanupDate = new Date(statistics.next_cleanup_at);
                      // Check if the date is valid
                      if (isNaN(nextCleanupDate.getTime())) {
                        return "Not scheduled";
                      }
                      return format(nextCleanupDate, "PPP 'at' p");
                    } catch (e) {
                      console.error("Error formatting next cleanup date:", e);
                      return "Not scheduled";
                    }
                  })()}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Cleanup Results */}
        {cleanupResults && (
          <Alert className="bg-green-50 border-green-200">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">
              Cleanup Completed
            </AlertTitle>
            <AlertDescription className="text-green-700">
              <div className="mt-2">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Data Type</TableHead>
                      <TableHead className="text-right">
                        Records Removed
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell>Notifications</TableCell>
                      <TableCell className="text-right">
                        {cleanupResults.notifications_deleted}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Attendance Records</TableCell>
                      <TableCell className="text-right">
                        {cleanupResults.attendance_records_deleted}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Audit Logs</TableCell>
                      <TableCell className="text-right">
                        {cleanupResults.audit_logs_deleted}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Excuses</TableCell>
                      <TableCell className="text-right">
                        {cleanupResults.excuses_deleted}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Location Alerts</TableCell>
                      <TableCell className="text-right">
                        {cleanupResults.location_alerts_deleted || 0}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Biometric Credentials</TableCell>
                      <TableCell className="text-right">
                        {cleanupResults.biometric_credentials_deleted || 0}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>System Logs</TableCell>
                      <TableCell className="text-right">
                        {cleanupResults.system_logs_deleted || 0}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>User Activity Logs</TableCell>
                      <TableCell className="text-right">
                        {cleanupResults.user_activity_logs_deleted || 0}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>QR Sessions</TableCell>
                      <TableCell className="text-right">
                        {cleanupResults.qr_sessions_deleted || 0}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
                <div className="mt-2 text-sm">
                  Next scheduled cleanup:{" "}
                  {(() => {
                    try {
                      const nextCleanupDate = new Date(
                        cleanupResults.next_cleanup
                      );
                      // Check if the date is valid
                      if (isNaN(nextCleanupDate.getTime())) {
                        return "Not scheduled (invalid date)";
                      }
                      return format(nextCleanupDate, "PPP");
                    } catch (e) {
                      console.error(
                        "Error formatting cleanup results next date:",
                        e
                      );
                      return "Not scheduled (error)";
                    }
                  })()}
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Warning Alert */}
        <Alert variant="destructive" className="bg-red-50 border-red-200">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertTitle className="text-red-800">Important Note</AlertTitle>
          <AlertDescription className="text-red-700">
            Database cleanup permanently removes data based on your retention
            settings. This action cannot be undone. Make sure your retention
            periods are appropriate for your school's needs and compliance
            requirements.
          </AlertDescription>
        </Alert>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleTriggerCleanup}
          disabled={cleanupRunning || saving}
        >
          {cleanupRunning ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Running Cleanup...
            </>
          ) : (
            <>
              <Trash2 className="h-4 w-4 mr-2" />
              Run Cleanup Now
            </>
          )}
        </Button>
        <Button
          onClick={handleSaveSettings}
          disabled={saving || cleanupRunning}
        >
          {saving ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            "Save Settings"
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
