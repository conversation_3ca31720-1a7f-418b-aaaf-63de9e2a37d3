import { useEffect } from "react";
import { automatedReminderService } from "@/lib/services/automated-reminder-service";
import { useAuth } from "@/context/AuthContext";

/**
 * Hook to initialize and manage automated reminder service
 * Only starts the service for admin users
 */
export function useAutomatedReminders() {
  const { profile } = useAuth();

  useEffect(() => {
    // Only start reminder service for admin users
    if (profile?.role === "admin") {
      automatedReminderService.start();
    }

    // Cleanup on unmount
    return () => {
      if (profile?.role === "admin") {
        automatedReminderService.stop();
      }
    };
  }, [profile?.role]);

  return {
    getReminderSettings: automatedReminderService.getReminderSettings.bind(automatedReminderService),
    updateReminderSettings: automatedReminderService.updateReminderSettings.bind(automatedReminderService),
    sendManualReminder: automatedReminderService.sendManualReminder.bind(automatedReminderService),
    getStatus: automatedReminderService.getStatus.bind(automatedReminderService),
  };
}
