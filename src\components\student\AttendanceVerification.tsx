import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, MapPin, AlertCircle, CheckCircle2, XCircle } from "lucide-react";
import { supabase } from '@/lib/supabase';
import { verifyLocationForAttendance } from '@/lib/location-utils';
import { useAuth } from '@/hooks/useAuth';
import { useTranslation } from "react-i18next";

interface AttendanceVerificationProps {
  sessionId: string;
  roomId: string;
}

export function AttendanceVerification({ sessionId, roomId }: AttendanceVerificationProps) {
  const [loading, setLoading] = useState(false);
  const [location, setLocation] = useState<GeolocationPosition | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();
  const { t } = useTranslation();

  useEffect(() => {
    // Request location permission when component mounts
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => setLocation(position),
        (error) => {
          console.error('Error getting location:', error);
          toast({
            title: 'Location Error',
            description: 'Please enable location services to verify attendance.',
            variant: 'destructive',
          });
        }
      );
    }
  }, []);

  const handleVerification = async () => {
    if (!location || !user) return;

    setLoading(true);
    try {
      // Get student's assigned room coordinates and configured radius
      const { data: roomData, error: roomError } = await supabase
        .from('rooms')
        .select('id, latitude, longitude, room_number')
        .eq('room_number', user.roomNumber)
        .single();

      if (roomError) throw roomError;
      if (!roomData) throw new Error('Room not found');

      // Get the configured radius for this room
      const { data: roomLocationData, error: roomLocationError } = await supabase
        .from('room_locations')
        .select('radius_meters')
        .eq('room_id', roomData.id)
        .single();

      // Use configured radius or default to 50m
      const allowedRadius = roomLocationData?.radius_meters || 50;

      // Check distance and create alert if needed
      const { data: distanceCheck, error: distanceError } = await supabase
        .rpc('handle_distance_alert', {
          student_id: user.id,
          student_lat: location.coords.latitude,
          student_lng: location.coords.longitude,
          room_lat: roomData.latitude,
          room_lng: roomData.longitude,
          max_distance_meters: allowedRadius
        });

      if (distanceError) throw distanceError;

      if (!distanceCheck[0].is_within_radius) {
        toast({
          title: 'Distance Alert',
          description: `You are too far from your assigned room (${Math.round(distanceCheck[0].distance)}m). Please move closer to verify attendance.`,
          variant: 'destructive',
        });
        return;
      }

      // If within radius, proceed with attendance verification
      const { error: attendanceError } = await supabase
        .from('attendance_records')
        .insert({
          student_id: user.id,
          verified_at: new Date().toISOString(),
          location: `POINT(${location.coords.longitude} ${location.coords.latitude})`,
          distance_meters: distanceCheck[0].distance
        });

      if (attendanceError) throw attendanceError;

      toast({
        title: 'Success',
        description: 'Attendance verified successfully!',
      });
    } catch (error: any) {
      console.error('Verification error:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to verify attendance',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const renderLocationPermissionMessage = () => {
    if (!location) {
      return (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Location Access Required</AlertTitle>
          <AlertDescription>
            Location access is blocked. Please allow location access in your browser settings to verify attendance.
            <Button
              variant="link"
              className="px-0 text-destructive underline"
              onClick={() => window.open('chrome://settings/content/location', '_blank')}
            >
              Open Browser Settings
            </Button>
          </AlertDescription>
        </Alert>
      );
    }

    return null;
  };

  const renderVerificationResult = () => {
    if (!location) return null;

    return (
      <Alert 
        variant="info" 
        className="mb-4"
      >
        <CheckCircle2 className="h-4 w-4" />
        <AlertTitle>Location Verified</AlertTitle>
        <AlertDescription>
          You are within the allowed radius of your assigned room.
        </AlertDescription>
      </Alert>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Attendance Verification</CardTitle>
        <CardDescription>
          Verify your attendance by confirming your location
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {renderLocationPermissionMessage()}
        {renderVerificationResult()}
        
        <Button
          onClick={handleVerification}
          disabled={loading || !location}
          className="w-full flex items-center gap-2"
        >
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <MapPin className="h-4 w-4" />
          )}
          {loading ? t("loading.verifying") : t("attendance.verifyAttendance")}
        </Button>
      </CardContent>
    </Card>
  );
} 