import { useState } from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Fingerprint,
  Shield,
  Zap,
  Eye,
  Users,
  GraduationCap,
  UserCheck,
  Settings,
  Sparkles,
  CheckCircle,
  ArrowRight,
  Lock,
  Smartphone
} from "lucide-react";
import BiometricSignIn from "@/components/auth/BiometricSignIn";
import { isWebAuthnAvailable } from "@/lib/webauthn";

export default function BiometricSignInDemo() {
  const [activeDemo, setActiveDemo] = useState<'overview' | 'signin' | 'benefits'>('overview');
  const isSupported = isWebAuthnAvailable();

  const userTypes = [
    {
      icon: GraduationCap,
      title: "Students",
      description: "Fast attendance + quick dashboard access",
      benefits: ["One-touch sign-in", "Secure QR scanning", "Mobile-friendly"],
      color: "bg-blue-500"
    },
    {
      icon: UserCheck,
      title: "Teachers",
      description: "Instant access to class management",
      benefits: ["Quick dashboard access", "Secure grading", "Time-saving"],
      color: "bg-green-500"
    },
    {
      icon: Settings,
      title: "School Admins",
      description: "Enhanced security for admin functions",
      benefits: ["Biometric protection", "Audit trails", "Efficient access"],
      color: "bg-purple-500"
    }
  ];

  const features = [
    {
      icon: Zap,
      title: "Lightning Fast",
      description: "Sign in with just a touch or glance",
      color: "text-yellow-600"
    },
    {
      icon: Shield,
      title: "Ultra Secure",
      description: "WebAuthn standard with cryptographic verification",
      color: "text-green-600"
    },
    {
      icon: Eye,
      title: "Privacy First",
      description: "Biometric data never leaves your device",
      color: "text-purple-600"
    },
    {
      icon: Smartphone,
      title: "Universal Support",
      description: "Works on desktop, mobile, and tablets",
      color: "text-blue-600"
    }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-center gap-3"
        >
          <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl">
            <Fingerprint className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900">
            Biometric Sign-In
          </h1>
          <Sparkles className="w-6 h-6 text-yellow-500" />
        </motion.div>
        
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Secure, fast, and beautiful authentication for students, teachers, and school administrators
        </p>

        <div className="flex items-center justify-center gap-2">
          <Badge variant={isSupported ? "default" : "secondary"} className="px-3 py-1">
            {isSupported ? (
              <>
                <CheckCircle className="w-4 h-4 mr-1" />
                Supported on this device
              </>
            ) : (
              <>
                <Lock className="w-4 h-4 mr-1" />
                Requires HTTPS or modern browser
              </>
            )}
          </Badge>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-center">
        <div className="flex bg-gray-100 rounded-lg p-1">
          {[
            { id: 'overview', label: 'Overview', icon: Eye },
            { id: 'signin', label: 'Try Demo', icon: Fingerprint },
            { id: 'benefits', label: 'Benefits', icon: Users }
          ].map(({ id, label, icon: Icon }) => (
            <Button
              key={id}
              variant={activeDemo === id ? "default" : "ghost"}
              onClick={() => setActiveDemo(id as any)}
              className="flex items-center gap-2"
            >
              <Icon className="w-4 h-4" />
              {label}
            </Button>
          ))}
        </div>
      </div>

      {/* Content */}
      <motion.div
        key={activeDemo}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {activeDemo === 'overview' && (
          <div className="space-y-8">
            {/* Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="text-center h-full">
                    <CardContent className="p-6">
                      <div className="flex justify-center mb-4">
                        <div className="p-3 bg-gray-100 rounded-full">
                          <feature.icon className={`w-6 h-6 ${feature.color}`} />
                        </div>
                      </div>
                      <h3 className="font-semibold text-lg mb-2">{feature.title}</h3>
                      <p className="text-gray-600 text-sm">{feature.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>

            {/* How It Works */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ArrowRight className="w-5 h-5" />
                  How It Works
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <span className="text-blue-600 font-bold">1</span>
                    </div>
                    <h4 className="font-semibold mb-2">Register Biometrics</h4>
                    <p className="text-sm text-gray-600">
                      Set up fingerprint or face recognition in your profile settings
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <span className="text-green-600 font-bold">2</span>
                    </div>
                    <h4 className="font-semibold mb-2">Enter Email</h4>
                    <p className="text-sm text-gray-600">
                      Type your email address on the login page
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <span className="text-purple-600 font-bold">3</span>
                    </div>
                    <h4 className="font-semibold mb-2">Authenticate</h4>
                    <p className="text-sm text-gray-600">
                      Touch sensor or use face recognition to sign in instantly
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {activeDemo === 'signin' && (
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader className="text-center">
                <CardTitle>Try Biometric Sign-In</CardTitle>
                <p className="text-sm text-gray-600">
                  Experience the future of authentication
                </p>
              </CardHeader>
              <CardContent>
                <BiometricSignIn
                  onSuccess={() => {
                    // Demo: Biometric sign-in successful
                  }}
                  onError={(error) => {
                    // Demo: Biometric sign-in error
                  }}
                  className="border-0 shadow-none"
                />
              </CardContent>
            </Card>
          </div>
        )}

        {activeDemo === 'benefits' && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4">Benefits for Every User Type</h2>
              <p className="text-gray-600">
                Biometric authentication enhances the experience for all users
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {userTypes.map((userType, index) => (
                <motion.div
                  key={userType.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="h-full">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-3 mb-4">
                        <div className={`p-2 ${userType.color} rounded-lg`}>
                          <userType.icon className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg">{userType.title}</h3>
                          <p className="text-sm text-gray-600">{userType.description}</p>
                        </div>
                      </div>
                      
                      <Separator className="my-4" />
                      
                      <div className="space-y-2">
                        {userType.benefits.map((benefit, idx) => (
                          <div key={idx} className="flex items-center gap-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span className="text-sm">{benefit}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        )}
      </motion.div>

      {/* Call to Action */}
      <div className="text-center">
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-8">
            <h3 className="text-xl font-bold mb-2">Ready to Get Started?</h3>
            <p className="text-gray-600 mb-4">
              Register your biometrics in profile settings to enable this feature
            </p>
            <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
              <Settings className="w-4 h-4 mr-2" />
              Go to Profile Settings
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
