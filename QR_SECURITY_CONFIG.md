# 🔐 QR Code Security Configuration

## 📋 Overview

The QR code system now supports customizable rotating challenge intervals through environment variables. This allows you to adjust the security vs usability balance without modifying code.

## ⚙️ Configuration Options

### **1. Challenge Rotation Interval**

```bash
NEXT_PUBLIC_QR_CHALLENGE_ROTATION_SECONDS=30
```

**Description**: How often the rotating challenge changes (in seconds)
**Default**: 30 seconds
**Range**: 5-300 seconds (5 seconds to 5 minutes)
**Impact**: 
- **Lower values** = Higher security, shorter screenshot window
- **Higher values** = Better usability, longer valid window

### **2. Grace Period Slots**

```bash
NEXT_PUBLIC_QR_CHALLENGE_GRACE_SLOTS=1
```

**Description**: Number of previous challenge slots that remain valid
**Default**: 1 slot (allows previous challenge)
**Range**: 0-5 slots
**Impact**:
- **0 slots** = Only current challenge valid (strictest)
- **1 slot** = Current + previous challenge valid (recommended)
- **2+ slots** = Longer grace period (more forgiving)

## 🎯 Security Scenarios

### **High Security (Strict)**
```bash
NEXT_PUBLIC_QR_CHALLENGE_ROTATION_SECONDS=15
NEXT_PUBLIC_QR_CHALLENGE_GRACE_SLOTS=0
```
- Challenge changes every 15 seconds
- Only current challenge valid
- **Total validity window**: 15 seconds
- **Use case**: High-security environments, exam halls

### **Balanced Security (Recommended)**
```bash
NEXT_PUBLIC_QR_CHALLENGE_ROTATION_SECONDS=30
NEXT_PUBLIC_QR_CHALLENGE_GRACE_SLOTS=1
```
- Challenge changes every 30 seconds
- Current + previous challenge valid
- **Total validity window**: 60 seconds
- **Use case**: Normal attendance tracking

### **User-Friendly (Relaxed)**
```bash
NEXT_PUBLIC_QR_CHALLENGE_ROTATION_SECONDS=60
NEXT_PUBLIC_QR_CHALLENGE_GRACE_SLOTS=2
```
- Challenge changes every 60 seconds
- Current + 2 previous challenges valid
- **Total validity window**: 180 seconds (3 minutes)
- **Use case**: Large classes, slower devices

### **Development/Testing**
```bash
NEXT_PUBLIC_QR_CHALLENGE_ROTATION_SECONDS=120
NEXT_PUBLIC_QR_CHALLENGE_GRACE_SLOTS=3
```
- Challenge changes every 2 minutes
- Current + 3 previous challenges valid
- **Total validity window**: 480 seconds (8 minutes)
- **Use case**: Development and testing

## 📊 Security vs Usability Matrix

| Rotation (s) | Grace Slots | Total Window | Security Level | Usability |
|--------------|-------------|--------------|----------------|-----------|
| 10           | 0           | 10s          | 🔴 Maximum     | 🔴 Poor   |
| 15           | 0           | 15s          | 🔴 Very High   | 🟡 Fair   |
| 30           | 1           | 60s          | 🟡 High        | 🟢 Good   |
| 60           | 1           | 120s         | 🟡 Medium      | 🟢 Good   |
| 60           | 2           | 180s         | 🟠 Medium-Low  | 🟢 Great  |
| 120          | 3           | 480s         | 🟠 Low         | 🟢 Great  |

## 🛠️ Implementation Details

### **How It Works**

1. **Time Slot Calculation**:
   ```typescript
   const timeSlot = Math.floor(currentTime / ROTATION_INTERVAL);
   ```

2. **Challenge Generation**:
   ```typescript
   const challenge = SHA256(`${timeSlot}:${SECRET_KEY}`).substring(0, 16);
   ```

3. **Validation Window**:
   ```typescript
   // Check current and previous slots
   for (let offset = 0; offset <= GRACE_SLOTS; offset++) {
     const slot = currentSlot - offset;
     // Validate against this slot's challenge
   }
   ```

### **Configuration Validation**

The system validates configuration on startup:

```typescript
// Minimum 5 seconds to prevent abuse
if (ROTATION_INTERVAL < 5) {
  console.warn("Challenge rotation too fast, using minimum 5 seconds");
}

// Maximum 5 minutes to maintain security
if (ROTATION_INTERVAL > 300) {
  console.warn("Challenge rotation too slow, using maximum 300 seconds");
}
```

## 🔍 Monitoring & Debugging

### **Get Current Configuration**

```typescript
import { getQRSecurityConfig, getQRSecurityInfo } from '@/lib/utils/qr-security';

// Get detailed config
const config = getQRSecurityConfig();
console.log(config);
// {
//   challengeRotationInterval: 30,
//   challengeGracePeriodSlots: 1,
//   totalGracePeriodSeconds: 30,
//   effectiveValidityWindow: 60
// }

// Get human-readable info
const info = getQRSecurityInfo();
console.log(info);
// "QR Challenge rotates every 30s, valid for 60s total"
```

### **Runtime Configuration Check**

Add this to your admin dashboard to monitor current settings:

```typescript
const securityInfo = getQRSecurityInfo();
// Display in admin panel for monitoring
```

## 🚨 Security Considerations

### **⚠️ Important Notes**

1. **Longer intervals = Higher risk**: Screenshots remain valid longer
2. **Grace periods = Attack window**: More slots = longer vulnerability
3. **Network latency**: Consider network delays when setting intervals
4. **Device performance**: Slower devices may need longer grace periods

### **🔒 Best Practices**

1. **Start conservative**: Begin with default settings (30s/1 slot)
2. **Monitor usage**: Track failed scans to adjust grace periods
3. **Environment-specific**: Use stricter settings for sensitive areas
4. **Regular review**: Periodically assess and adjust based on usage patterns

### **🎯 Recommended Settings by Use Case**

- **Exam Halls**: 15s rotation, 0 grace slots
- **Regular Classes**: 30s rotation, 1 grace slot  
- **Large Assemblies**: 60s rotation, 2 grace slots
- **Development**: 120s rotation, 3 grace slots

## 🔄 Changing Configuration

### **1. Update Environment Variables**

```bash
# In your .env file
NEXT_PUBLIC_QR_CHALLENGE_ROTATION_SECONDS=45
NEXT_PUBLIC_QR_CHALLENGE_GRACE_SLOTS=2
```

### **2. Restart Application**

```bash
npm run dev  # Development
# or
npm run build && npm start  # Production
```

### **3. Verify Changes**

Check the admin dashboard or console logs to confirm new settings are active.

---

**🎉 You can now customize QR challenge rotation without touching the code!**
