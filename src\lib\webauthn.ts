import { supabase } from "./supabase";

// Convert ArrayBuffer to Base64URL string
const bufferToBase64URL = (buffer: ArrayBuffer): string => {
  // Convert ArrayBuffer to Base64
  const base64 = btoa(String.fromCharCode(...new Uint8Array(buffer)));
  // Convert Base64 to Base64URL
  return base64.replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
};

// Convert Base64URL string to ArrayBuffer
const base64URLToBuffer = (base64url: string): ArrayBuffer => {
  // Convert Base64URL to Base64
  const base64 = base64url.replace(/-/g, "+").replace(/_/g, "/");
  // Add padding if needed
  const padded = base64.padEnd(
    base64.length + ((4 - (base64.length % 4)) % 4),
    "="
  );
  // Convert Base64 to binary string
  const binary = atob(padded);
  // Convert binary string to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  const buffer = new ArrayBuffer(binary.length);
  const bytes = new Uint8Array(buffer);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return buffer;
};

// Get the domain without port number
const getDomain = () => {
  const hostname = window.location.hostname;

  // For localhost development, we need to handle WebAuthn differently
  if (
    hostname === "localhost" ||
    hostname === "127.0.0.1" ||
    hostname.includes("localhost")
  ) {
    // For development, we'll use localhost but need to ensure HTTPS or special handling
    return "localhost";
  }

  // For IP addresses (like 192.168.x.x), WebAuthn requires special handling
  // IP addresses are not valid RP IDs, so we need to use localhost for development
  const isIPAddress = /^(\d{1,3}\.){3}\d{1,3}$/.test(hostname);
  if (isIPAddress) {
    console.warn(
      `WebAuthn: IP address detected (${hostname}), using localhost as RP ID`
    );
    return "localhost";
  }

  // For production, use the actual hostname
  return hostname;
};

// Check if we're in a secure context (required for WebAuthn)
const isSecureContext = () => {
  const hostname = window.location.hostname;
  const isIPAddress = /^(\d{1,3}\.){3}\d{1,3}$/.test(hostname);

  // WebAuthn requires HTTPS except for localhost and local IP addresses in development
  return (
    window.isSecureContext ||
    hostname === "localhost" ||
    hostname === "127.0.0.1" ||
    window.location.protocol === "https:" ||
    (isIPAddress && window.location.protocol === "https:") // Allow HTTPS on IP addresses
  );
};

// Check if WebAuthn is available
export const isWebAuthnAvailable = () => {
  return (
    window.PublicKeyCredential !== undefined &&
    typeof window.PublicKeyCredential === "function" &&
    isSecureContext()
  );
};

export async function startRegistration(userId: string, username: string) {
  try {
    if (!isWebAuthnAvailable()) {
      const reasons = [];
      if (!window.PublicKeyCredential) reasons.push("WebAuthn not supported");
      if (!isSecureContext()) reasons.push("Requires HTTPS or localhost");
      throw new Error(`WebAuthn is not available: ${reasons.join(", ")}`);
    }

    // 1. Get registration options from server
    const challenge = new Uint8Array(32);
    crypto.getRandomValues(challenge);

    const domain = getDomain();
    console.log("WebAuthn registration - using domain:", domain);
    console.log("Current location:", window.location.href);
    console.log("Secure context:", isSecureContext());

    const publicKeyCredentialCreationOptions: PublicKeyCredentialCreationOptions =
      {
        challenge,
        rp: {
          name: "Attendance Tracking System",
          id: domain,
        },
        user: {
          id: new TextEncoder().encode(userId),
          name: username,
          displayName: username,
        },
        pubKeyCredParams: [
          { alg: -7, type: "public-key" }, // ES256
          { alg: -257, type: "public-key" }, // RS256
        ],
        authenticatorSelection: {
          // Remove platform restriction to support both platform and cross-platform authenticators
          authenticatorAttachment: undefined,
          userVerification: "preferred",
          requireResidentKey: false,
        },
        timeout: 60000,
        attestation: "none",
      };

    // 2. Create credentials
    const credential = (await navigator.credentials.create({
      publicKey: publicKeyCredentialCreationOptions,
    })) as PublicKeyCredential;

    if (!credential) {
      throw new Error("No credentials returned from authenticator");
    }

    const response = credential.response as AuthenticatorAttestationResponse;

    // 3. Format the response data
    const credentialData = {
      id: credential.id,
      rawId: bufferToBase64URL(credential.rawId),
      response: {
        attestationObject: bufferToBase64URL(response.attestationObject),
        clientDataJSON: bufferToBase64URL(response.clientDataJSON),
      },
      type: credential.type,
    };

    // 4. Delete any existing credentials for this user (only one allowed)
    const { error: deleteError } = await supabase
      .from("biometric_credentials")
      .delete()
      .eq("user_id", userId);

    if (deleteError) {
      console.error("Error deleting existing credentials:", deleteError);
      // Don't throw here, continue with registration
    }

    // 5. Store new credential in Supabase
    const { error } = await supabase.from("biometric_credentials").insert([
      {
        user_id: userId,
        credential_id: credentialData.id,
        public_key: credentialData.rawId,
        counter: 0,
        created_at: new Date().toISOString(),
      },
    ]);

    if (error) throw error;

    // 6. Update the profiles table to mark biometric as registered
    const { error: profileError } = await supabase
      .from("profiles")
      .update({ biometric_registered: true })
      .eq("user_id", userId);

    if (profileError) {
      console.error("Error updating profile biometric status:", profileError);
      // Don't throw here as the credential was successfully stored
    }

    return credentialData;
  } catch (error) {
    console.error("Error in biometric registration:", error);
    throw error;
  }
}

export async function startAuthentication(userId: string) {
  try {
    if (!isWebAuthnAvailable()) {
      throw new Error("WebAuthn is not supported in this browser");
    }

    // 1. Get the user's credentials from the database using RPC to bypass RLS
    const { data: credentialsData, error } = await supabase.rpc('get_biometric_credentials_for_auth', {
      target_user_id: userId
    });

    if (error) {
      console.error("Error fetching credentials:", error);
      throw new Error(
        "Failed to fetch biometric credentials. Please try registering again."
      );
    }

    if (!credentialsData) {
      throw new Error(
        "No biometric credentials found. Please register your biometrics first."
      );
    }

    const credentials = credentialsData;

    // 2. Create authentication options
    const challenge = new Uint8Array(32);
    crypto.getRandomValues(challenge);

    const publicKeyCredentialRequestOptions: PublicKeyCredentialRequestOptions =
      {
        challenge,
        allowCredentials: [
          {
            id: base64URLToBuffer(credentials.public_key),
            type: "public-key",
            transports: ["internal", "hybrid", "usb", "ble", "nfc"],
          },
        ],
        timeout: 60000,
        userVerification: "preferred",
        rpId: getDomain(),
      };

    // 3. Request authentication
    const assertion = (await navigator.credentials.get({
      publicKey: publicKeyCredentialRequestOptions,
    })) as PublicKeyCredential;

    if (!assertion) {
      throw new Error("No credentials returned from authenticator");
    }

    const response = assertion.response as AuthenticatorAssertionResponse;

    // 4. Format the response data
    const assertionData = {
      id: assertion.id,
      rawId: bufferToBase64URL(assertion.rawId),
      response: {
        authenticatorData: bufferToBase64URL(response.authenticatorData),
        clientDataJSON: bufferToBase64URL(response.clientDataJSON),
        signature: bufferToBase64URL(response.signature),
        userHandle: response.userHandle
          ? bufferToBase64URL(response.userHandle)
          : null,
      },
      type: assertion.type,
    };

    // 5. Update the credential counter in the database
    const { error: updateError } = await supabase
      .from("biometric_credentials")
      .update({ counter: (credentials.counter || 0) + 1 })
      .eq("credential_id", credentials.credential_id)
      .throwOnError();

    if (updateError) {
      console.error("Error updating credential counter:", updateError);
      // Don't throw here, as the authentication was successful
    }

    return assertionData;
  } catch (error) {
    console.error("Error in biometric authentication:", error);

    // Provide more helpful error messages
    if (error instanceof Error) {
      if (
        error.message.includes("invalid domain") ||
        error.message.includes("SecurityError")
      ) {
        throw new Error(
          "Invalid domain for biometric authentication. Please access the app via HTTPS."
        );
      }
      if (error.message.includes("NotAllowedError")) {
        throw new Error(
          "Biometric authentication was cancelled or not allowed."
        );
      }
      if (error.message.includes("NotSupportedError")) {
        throw new Error(
          "Biometric authentication is not supported on this device."
        );
      }
      throw new Error(`Authentication failed: ${error.message}`);
    }
    throw new Error("Authentication failed. Please try again.");
  }
}

export async function isPasskeyAvailable(userId: string): Promise<boolean> {
  try {
    // Check if WebAuthn is supported
    if (!window.PublicKeyCredential) {
      return false;
    }

    // Check if any authenticator is available (not just platform)
    try {
      const platformAuth =
        await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
      // Even if platform authenticator is not available, we'll allow cross-platform ones
      if (!platformAuth) {
        console.log(
          "Platform authenticator not available, falling back to cross-platform"
        );
      }
    } catch (error) {
      console.log(
        "Error checking platform authenticator, falling back to cross-platform"
      );
    }

    // Check if user has registered credentials using RPC
    const { data: credentialsData, error } = await supabase.rpc('get_biometric_credentials_for_auth', {
      target_user_id: userId
    });

    if (error) {
      console.error("Error checking biometric credentials:", error);
      return false;
    }

    return !!credentialsData;
  } catch (error) {
    console.error("Error checking passkey availability:", error);
    return false;
  }
}

export function isBiometricsAvailable(): boolean {
  return (
    window.PublicKeyCredential !== undefined &&
    PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable !==
      undefined
  );
}
