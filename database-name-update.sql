-- =====================================================
-- DATABASE UPDATE SCRIPT: Campus Guardian → Attendance Tracking System
-- =====================================================
-- Run this script in your Supabase SQL Editor to update existing database records
-- This script is safe to run multiple times

-- 1. Update footer_settings table (if it exists)
UPDATE public.footer_settings 
SET 
  developer_name = 'Attendance Tracking System Team',
  developer_website = 'https://attendancetracking.edu',
  contact_email = '<EMAIL>',
  updated_at = NOW()
WHERE 
  developer_name = 'Campus Guardian Team' 
  OR developer_website = 'https://campusguardian.edu'
  OR contact_email = '<EMAIL>';

-- 2. Update system_settings table - Email service config
UPDATE public.system_settings 
SET 
  setting_value = jsonb_set(
    setting_value,
    '{fromEmail}',
    '"<EMAIL>"'
  ),
  updated_at = NOW()
WHERE 
  setting_name = 'email_service_config' 
  AND setting_value->>'fromEmail' = '<EMAIL>';

-- 3. Update parent notification templates (if they exist in system_settings)
UPDATE public.system_settings 
SET 
  setting_value = replace(
    setting_value::text,
    'Campus Guardian Attendance System',
    'Attendance Tracking System'
  )::jsonb,
  updated_at = NOW()
WHERE 
  setting_name LIKE '%notification%' 
  AND setting_value::text LIKE '%Campus Guardian%';

-- 4. Update school_settings table - Custom login messages
UPDATE public.school_settings 
SET 
  custom_login_message = replace(
    custom_login_message,
    'Campus Guardian',
    'Attendance Tracking System'
  ),
  updated_at = NOW()
WHERE 
  custom_login_message LIKE '%Campus Guardian%';

-- 5. Update system_settings_overrides table - Custom messages
UPDATE public.system_settings_overrides 
SET 
  setting_value = replace(
    setting_value::text,
    'Campus Guardian',
    'Attendance Tracking System'
  )::jsonb,
  updated_at = NOW()
WHERE 
  setting_value::text LIKE '%Campus Guardian%';

-- 6. Update any custom dashboard messages
UPDATE public.school_settings 
SET 
  custom_student_message = replace(
    custom_student_message,
    'Campus Guardian',
    'Attendance Tracking System'
  ),
  custom_teacher_message = replace(
    custom_teacher_message,
    'Campus Guardian',
    'Attendance Tracking System'
  ),
  custom_admin_message = replace(
    custom_admin_message,
    'Campus Guardian',
    'Attendance Tracking System'
  ),
  updated_at = NOW()
WHERE 
  custom_student_message LIKE '%Campus Guardian%'
  OR custom_teacher_message LIKE '%Campus Guardian%'
  OR custom_admin_message LIKE '%Campus Guardian%';

-- 7. Show results of updates
SELECT 
  'footer_settings' as table_name,
  COUNT(*) as updated_records
FROM public.footer_settings 
WHERE developer_name = 'Attendance Tracking System Team'

UNION ALL

SELECT 
  'system_settings (email)',
  COUNT(*)
FROM public.system_settings 
WHERE setting_name = 'email_service_config' 
  AND setting_value->>'fromEmail' = '<EMAIL>'

UNION ALL

SELECT 
  'Updated records',
  0 as count;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================
-- Run these to verify the updates were successful:

-- Check footer settings
-- SELECT * FROM public.footer_settings;

-- Check email config
-- SELECT setting_name, setting_value FROM public.system_settings WHERE setting_name = 'email_service_config';

-- Check for any remaining "Campus Guardian" references
-- SELECT table_name, column_name 
-- FROM information_schema.columns 
-- WHERE table_schema = 'public' 
--   AND data_type IN ('text', 'character varying', 'jsonb');

-- =====================================================
-- NOTES:
-- =====================================================
-- 1. This script only updates existing records
-- 2. New records will use the updated default values from migrations
-- 3. The script is safe to run multiple times
-- 4. If any table doesn't exist, those updates will be skipped
-- =====================================================
