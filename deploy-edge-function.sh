#!/bin/bash

# 🚀 Deploy QR Security Edge Function to Supabase
# This script automates the deployment process

set -e  # Exit on any error

PROJECT_REF="wclwxrilybnzkhvqzbmy"
FUNCTION_NAME="qr-security"

echo "🚀 Starting QR Security Edge Function Deployment..."
echo "Project: $PROJECT_REF"
echo "Function: $FUNCTION_NAME"
echo ""

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed!"
    echo "📦 Installing Supabase CLI..."
    npm install -g supabase
    echo "✅ Supabase CLI installed successfully"
fi

# Check if user is logged in
echo "🔐 Checking authentication..."
if ! supabase projects list &> /dev/null; then
    echo "❌ Not logged in to Supabase"
    echo "🔑 Please login to Supabase..."
    supabase login
    echo "✅ Successfully logged in"
else
    echo "✅ Already authenticated"
fi

# Link project if not already linked
echo "🔗 Linking project..."
if ! supabase status &> /dev/null; then
    echo "🔗 Linking to project $PROJECT_REF..."
    supabase link --project-ref $PROJECT_REF
    echo "✅ Project linked successfully"
else
    echo "✅ Project already linked"
fi

# Deploy the edge function
echo "📦 Deploying edge function..."
echo "Function: $FUNCTION_NAME"
supabase functions deploy $FUNCTION_NAME --project-ref $PROJECT_REF

if [ $? -eq 0 ]; then
    echo "✅ Edge function deployed successfully!"
else
    echo "❌ Edge function deployment failed!"
    exit 1
fi

# Run database migration
echo "🗄️ Running database migration..."
supabase db push --project-ref $PROJECT_REF

if [ $? -eq 0 ]; then
    echo "✅ Database migration completed successfully!"
else
    echo "❌ Database migration failed!"
    exit 1
fi

# Check if service role key is set
echo "🔑 Checking environment variables..."
echo ""
echo "⚠️  IMPORTANT: You need to set the SUPABASE_SERVICE_ROLE_KEY"
echo "📋 Steps to set the service role key:"
echo "1. Go to: https://supabase.com/dashboard/project/$PROJECT_REF/settings/api"
echo "2. Copy the 'service_role' key (NOT the anon key)"
echo "3. Run this command:"
echo "   supabase secrets set SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here --project-ref $PROJECT_REF"
echo ""

# Test the function
echo "🧪 Testing the deployed function..."
FUNCTION_URL="https://$PROJECT_REF.supabase.co/functions/v1/$FUNCTION_NAME"
echo "Function URL: $FUNCTION_URL"

# Test with a simple OPTIONS request to check CORS
echo "Testing CORS (OPTIONS request)..."
if curl -s -o /dev/null -w "%{http_code}" -X OPTIONS "$FUNCTION_URL" | grep -q "200"; then
    echo "✅ CORS test passed"
else
    echo "⚠️  CORS test failed - this is expected if service role key is not set"
fi

echo ""
echo "🎉 Deployment completed!"
echo ""
echo "📋 Next steps:"
echo "1. Set the service role key (see instructions above)"
echo "2. Test QR code generation in your app"
echo "3. Test QR code scanning in your app"
echo "4. Monitor function logs: supabase functions logs $FUNCTION_NAME --project-ref $PROJECT_REF"
echo ""
echo "🔍 Function URL: $FUNCTION_URL"
echo "📊 Dashboard: https://supabase.com/dashboard/project/$PROJECT_REF/functions/$FUNCTION_NAME"
echo ""
echo "✨ Your QR code system is now enterprise-grade secure!"
