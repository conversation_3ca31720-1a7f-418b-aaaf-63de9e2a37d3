import { useState, useEffect, useRef } from "react";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import { supabase } from "@/lib/supabase";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { getInitials } from "@/lib/utils";
import { useTranslation } from "react-i18next";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { RealtimeChannel } from "@supabase/supabase-js";

interface CarouselImage {
  id: string;
  title: string;
  description: string | null;
  image_url: string;
}

interface SimpleCarouselProps {
  userType: "student" | "teacher" | "admin";
}

export default function SimpleCarousel({ userType }: SimpleCarouselProps) {
  const { profile } = useAuth();
  const { currentSchool } = useSchool();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [images, setImages] = useState<CarouselImage[]>([]);
  const autoplayRef = useRef(
    Autoplay({ delay: 5000, stopOnInteraction: true, stopOnMouseEnter: true })
  );
  const subscriptionRef = useRef<RealtimeChannel | null>(null);

  useEffect(() => {
    if (currentSchool?.id) {
      loadCarouselImages();

      // Set up real-time subscription
      setupRealtimeSubscription();
    }

    // Cleanup subscription when component unmounts
    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current);
      }
    };
  }, [currentSchool, userType]);

  const setupRealtimeSubscription = async () => {
    // Remove any existing subscription
    if (subscriptionRef.current) {
      await supabase.removeChannel(subscriptionRef.current);
    }

    // Create a new subscription to the carousel_content table
    const channel = supabase
      .channel("carousel-changes")
      .on(
        "postgres_changes",
        {
          event: "*", // Listen for all events (INSERT, UPDATE, DELETE)
          schema: "public",
          table: "carousel_content",
          filter: `school_id=eq.${currentSchool.id}`,
        },
        (payload) => {
          console.log("Carousel content changed:", payload);
          // Reload carousel images when any change is detected
          loadCarouselImages();
        }
      )
      .subscribe();

    subscriptionRef.current = channel;
  };

  const loadCarouselImages = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("carousel_content")
        .select("id, title, description, image_url")
        .eq("school_id", currentSchool.id)
        .eq("active", true)
        .contains("target_audience", `{${userType}}`)
        .order("display_order", { ascending: true });

      if (error) {
        console.error("Error loading carousel content:", error);
        return;
      }

      // If no data from database, don't show anything
      if (!data || data.length === 0) {
        console.log("No carousel items found in database");
        setImages([]);
      } else {
        setImages(data);
      }
    } catch (error) {
      console.error("Error in carousel images fetch:", error);
    } finally {
      setLoading(false);
    }
  };

  // If no carousel items and not loading, return a placeholder div with 40px height on mobile and 80px on larger screens
  if (!loading && images.length === 0) {
    return (
      <div className="w-full h-[40px] sm:h-[80px] relative bg-transparent">
        {/* Title overlay */}
        <div className="absolute top-0 left-0 right-0 z-10 p-2 sm:p-4 bg-transparent">
          <div className="container mx-auto px-0 sm:px-4">
            <h1 className="text-sm sm:text-xl md:text-2xl font-bold text-[#08194A] dark:text-white pl-1 sm:pl-0 mt-0.5">
              {userType === "student" && t("dashboard.studentDashboard")}
              {userType === "teacher" && t("teacher.dashboard.title")}
              {userType === "admin" && t("admin.dashboard.title")}
            </h1>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full relative m-0 p-0">
      {loading ? (
        <div className="w-full h-[200px] md:h-[300px] overflow-hidden">
          <Skeleton className="w-full h-full" />
        </div>
      ) : (
        <div className="relative">
          {/* Title overlay */}
          <div className="absolute top-0 left-0 right-0 z-10 p-2 sm:p-4 bg-gradient-to-b from-black/70 to-transparent">
            <div className="container mx-auto px-0 sm:px-4">
              <h1 className="text-sm sm:text-xl md:text-2xl font-bold text-white pl-1 sm:pl-0 mt-0.5">
                {userType === "student" && t("dashboard.studentDashboard")}
                {userType === "teacher" && t("teacher.dashboard.title")}
                {userType === "admin" && t("admin.dashboard.title")}
                {userType === "admin" && currentSchool && (
                  <span className="text-white/80 ml-2 font-normal text-xs sm:text-sm md:text-base">
                    {currentSchool.name}
                  </span>
                )}
              </h1>
            </div>
          </div>
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            plugins={[autoplayRef.current]}
            className="w-full"
            onKeyDown={(e) => {
              // Add keyboard navigation
              if (e.key === "ArrowLeft") {
                const prevButton = document.querySelector(
                  "[data-carousel-prev]"
                );
                if (prevButton instanceof HTMLElement) {
                  prevButton.click();
                }
              } else if (e.key === "ArrowRight") {
                const nextButton = document.querySelector(
                  "[data-carousel-next]"
                );
                if (nextButton instanceof HTMLElement) {
                  nextButton.click();
                }
              }
            }}
          >
            <CarouselContent>
              {images.map((item) => (
                <CarouselItem key={item.id}>
                  <div className="relative w-full h-[200px] md:h-[300px] overflow-hidden">
                    <img
                      src={item.image_url}
                      alt={item.title}
                      className="w-full h-full object-cover"
                      loading="lazy"
                      onError={(e) => {
                        // Handle image loading errors
                        console.error("Error loading carousel image:", e);
                        // Set a fallback image
                        e.currentTarget.src =
                          "https://placehold.co/600x400/gray/white?text=Image+Not+Available";
                      }}
                    />

                    {/* Content overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-4 md:p-6">
                      <h3 className="text-white text-lg md:text-xl font-bold mb-1">
                        {item.title}
                      </h3>
                      {item.description && (
                        <div className="relative">
                          <div
                            className="max-h-[80px] md:max-h-[120px] overflow-y-auto custom-scrollbar pr-2"
                            ref={(el) => {
                              // Add a subtle indicator if content is scrollable
                              if (el && el.scrollHeight > el.clientHeight) {
                                el.classList.add(
                                  "after:absolute",
                                  "after:bottom-0",
                                  "after:left-0",
                                  "after:right-0",
                                  "after:h-4",
                                  "after:bg-gradient-to-t",
                                  "after:from-black/50",
                                  "after:to-transparent",
                                  "after:pointer-events-none"
                                );
                              }
                            }}
                          >
                            <p className="text-white/90 text-sm md:text-base">
                              {item.description}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>

            <CarouselPrevious
              variant="ghost"
              className="absolute left-2 sm:left-4 top-1/2 -translate-y-1/2 border-none text-white hover:bg-transparent"
              data-carousel-prev
              aria-label={t("carousel.previousSlide", "Previous slide")}
            />
            <CarouselNext
              variant="ghost"
              className="absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 border-none text-white hover:bg-transparent"
              data-carousel-next
              aria-label={t("carousel.nextSlide", "Next slide")}
            />
          </Carousel>

          {/* User avatar at the bottom */}
          <div className="absolute bottom-4 right-4 z-10">
            <Avatar className="h-10 w-10 border-2 border-white shadow-md">
              <AvatarImage
                src={profile?.photoUrl || ""}
                alt={profile?.name || ""}
              />
              <AvatarFallback className="bg-primary text-primary-foreground">
                {getInitials(profile?.name || "")}
              </AvatarFallback>
            </Avatar>
          </div>

          {/* Mobile swipe indicator - only shown on touch devices */}
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-10 md:hidden animate-pulse">
            <div className="flex items-center gap-1 bg-black/30 backdrop-blur-sm rounded-full px-3 py-1 text-white text-xs">
              <span className="inline-block w-4 h-1 bg-white rounded-full"></span>
              <span>{t("carousel.swipe", "Swipe")}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
