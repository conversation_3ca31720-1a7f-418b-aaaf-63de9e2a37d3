import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";

/**
 * A simple component to test that language changes are working
 */
export default function LanguageTest() {
  const { t, i18n } = useTranslation();

  // Check translations when the component renders
  useEffect(() => {
    // Translations are loaded and ready to use
  }, [t, i18n]);

  return (
    <div className="p-4 border rounded-md mb-4 bg-muted/20">
      <h3 className="text-lg font-medium mb-2">Language Test Component</h3>
      <p>
        <strong>Current Language:</strong> {i18n.language}
      </p>
      <ul className="list-disc pl-5 mt-2 space-y-1">
        <li>
          <strong>common.language:</strong> {t("common.language")}
        </li>
        <li>
          <strong>common.english:</strong> {t("common.english")}
        </li>
        <li>
          <strong>common.turkish:</strong> {t("common.turkish")}
        </li>
        <li>
          <strong>common.save:</strong> {t("common.save")}
        </li>
        <li>
          <strong>common.cancel:</strong> {t("common.cancel")}
        </li>
        <li>
          <strong>common.settings:</strong> {t("common.settings")}
        </li>
        <li>
          <strong>common.profile:</strong> {t("common.profile")}
        </li>
      </ul>
    </div>
  );
}
