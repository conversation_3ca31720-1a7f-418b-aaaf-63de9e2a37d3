-- Comprehensive Database Cleanup System Fix
-- This migration fixes all issues with the existing cleanup system

-- Drop existing function to recreate with proper parameters
DROP FUNCTION IF EXISTS perform_database_cleanup();
DROP FUNCTION IF EXISTS perform_database_cleanup(boolean);

-- Create improved database cleanup function with force parameter
CREATE OR REPLACE FUNCTION perform_database_cleanup(force_cleanup BOOLEAN DEFAULT false)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  settings RECORD;
  deleted_notifications INTEGER := 0;
  deleted_attendance_records INTEGER := 0;
  deleted_audit_logs INTEGER := 0;
  deleted_excuses INTEGER := 0;
  deleted_location_alerts INTEGER := 0;
  deleted_biometric_credentials INTEGER := 0;
  deleted_feedback_submissions INTEGER := 0;
  deleted_system_logs INTEGER := 0;
  deleted_user_activity_logs INTEGER := 0;
  deleted_qr_sessions INTEGER := 0;
  next_cleanup TIMESTAMP WITH TIME ZONE;
  selected_types JSONB;
  cleanup_results JSONB;
BEGIN
  -- Get the current settings
  SELECT * INTO settings FROM database_cleanup_settings LIMIT 1;
  
  -- If no settings found, exit
  IF settings IS NULL THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'No cleanup settings found',
      'timestamp', now()
    );
  END IF;
  
  -- If cleanup is not enabled and not forced, exit
  IF NOT settings.enabled AND NOT force_cleanup THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Cleanup is disabled',
      'timestamp', now()
    );
  END IF;
  
  -- Get selected data types
  selected_types := settings.selected_data_types;
  
  -- Clean up notifications if selected
  IF settings.notifications_retention_days > 0 AND (selected_types ? 'notifications' OR force_cleanup) THEN
    DELETE FROM notifications
    WHERE timestamp < (now() - (settings.notifications_retention_days || ' days')::interval);
    GET DIAGNOSTICS deleted_notifications = ROW_COUNT;
  END IF;
  
  -- Clean up attendance records if selected
  IF settings.attendance_records_retention_days > 0 AND (selected_types ? 'attendance_records' OR force_cleanup) THEN
    DELETE FROM attendance_records
    WHERE created_at < (now() - (settings.attendance_records_retention_days || ' days')::interval);
    GET DIAGNOSTICS deleted_attendance_records = ROW_COUNT;
  END IF;
  
  -- Clean up audit logs if selected (but keep recent cleanup logs)
  IF settings.audit_logs_retention_days > 0 AND (selected_types ? 'audit_logs' OR force_cleanup) THEN
    DELETE FROM audit_logs
    WHERE created_at < (now() - (settings.audit_logs_retention_days || ' days')::interval)
    AND action_type != 'database_cleanup';
    GET DIAGNOSTICS deleted_audit_logs = ROW_COUNT;
  END IF;
  
  -- Clean up excuses if selected (only completed ones)
  IF settings.excuses_retention_days > 0 AND (selected_types ? 'excuses' OR force_cleanup) THEN
    DELETE FROM excuses
    WHERE created_at < (now() - (settings.excuses_retention_days || ' days')::interval)
    AND status != 'pending';
    GET DIAGNOSTICS deleted_excuses = ROW_COUNT;
  END IF;
  
  -- Clean up location alerts if selected
  IF settings.location_alerts_retention_days > 0 AND (selected_types ? 'location_alerts' OR force_cleanup) THEN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'location_alerts') THEN
      DELETE FROM location_alerts
      WHERE created_at < (now() - (settings.location_alerts_retention_days || ' days')::interval);
      GET DIAGNOSTICS deleted_location_alerts = ROW_COUNT;
    END IF;
  END IF;
  
  -- Clean up old biometric credentials if selected (inactive for retention period)
  IF settings.biometric_credentials_retention_days > 0 AND (selected_types ? 'biometric_credentials' OR force_cleanup) THEN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'biometric_credentials') THEN
      DELETE FROM biometric_credentials
      WHERE updated_at < (now() - (settings.biometric_credentials_retention_days || ' days')::interval);
      GET DIAGNOSTICS deleted_biometric_credentials = ROW_COUNT;
    END IF;
  END IF;
  
  -- Clean up feedback submissions if selected (processed ones only)
  IF settings.feedback_submissions_retention_days > 0 AND (selected_types ? 'feedback_submissions' OR force_cleanup) THEN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'feedback_submissions') THEN
      DELETE FROM feedback_submissions
      WHERE created_at < (now() - (settings.feedback_submissions_retention_days || ' days')::interval)
      AND status != 'unread';
      GET DIAGNOSTICS deleted_feedback_submissions = ROW_COUNT;
    END IF;
  END IF;
  
  -- Clean up system logs if selected
  IF settings.system_logs_retention_days > 0 AND (selected_types ? 'system_logs' OR force_cleanup) THEN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_logs') THEN
      DELETE FROM system_logs
      WHERE created_at < (now() - (settings.system_logs_retention_days || ' days')::interval);
      GET DIAGNOSTICS deleted_system_logs = ROW_COUNT;
    END IF;
  END IF;
  
  -- Clean up user activity logs if selected
  IF settings.user_activity_logs_retention_days > 0 AND (selected_types ? 'user_activity_logs' OR force_cleanup) THEN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_activity_logs') THEN
      DELETE FROM user_activity_logs
      WHERE created_at < (now() - (settings.user_activity_logs_retention_days || ' days')::interval);
      GET DIAGNOSTICS deleted_user_activity_logs = ROW_COUNT;
    END IF;
  END IF;
  
  -- Clean up old QR sessions if selected
  IF settings.qr_sessions_retention_days > 0 AND (selected_types ? 'qr_sessions' OR force_cleanup) THEN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'qr_sessions') THEN
      DELETE FROM qr_sessions
      WHERE created_at < (now() - (settings.qr_sessions_retention_days || ' days')::interval);
      GET DIAGNOSTICS deleted_qr_sessions = ROW_COUNT;
    END IF;
  END IF;
  
  -- Calculate next cleanup date based on frequency
  IF settings.cleanup_frequency = 'daily' THEN
    next_cleanup := now() + interval '1 day';
  ELSIF settings.cleanup_frequency = 'weekly' THEN
    next_cleanup := now() + interval '7 days';
  ELSIF settings.cleanup_frequency = 'monthly' THEN
    next_cleanup := now() + interval '1 month';
  ELSE
    next_cleanup := now() + interval '7 days';
  END IF;
  
  -- Update the settings with the cleanup results
  UPDATE database_cleanup_settings
  SET 
    last_cleanup_at = now(),
    next_cleanup_at = next_cleanup,
    updated_at = now()
  WHERE id = settings.id;
  
  -- Build results object
  cleanup_results := jsonb_build_object(
    'success', true,
    'timestamp', now(),
    'next_cleanup', next_cleanup,
    'notifications_deleted', deleted_notifications,
    'attendance_records_deleted', deleted_attendance_records,
    'audit_logs_deleted', deleted_audit_logs,
    'excuses_deleted', deleted_excuses,
    'location_alerts_deleted', deleted_location_alerts,
    'biometric_credentials_deleted', deleted_biometric_credentials,
    'feedback_submissions_deleted', deleted_feedback_submissions,
    'system_logs_deleted', deleted_system_logs,
    'user_activity_logs_deleted', deleted_user_activity_logs,
    'qr_sessions_deleted', deleted_qr_sessions,
    'total_deleted', (
      deleted_notifications + 
      deleted_attendance_records + 
      deleted_audit_logs + 
      deleted_excuses + 
      deleted_location_alerts + 
      deleted_biometric_credentials + 
      deleted_feedback_submissions + 
      deleted_system_logs + 
      deleted_user_activity_logs + 
      deleted_qr_sessions
    )
  );
  
  -- Log the cleanup in audit_logs
  INSERT INTO audit_logs (
    action_type,
    entity_type,
    details,
    created_at
  ) VALUES (
    'database_cleanup',
    'system',
    cleanup_results,
    now()
  );
  
  RETURN cleanup_results;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION perform_database_cleanup(boolean) TO authenticated;

-- Update database_cleanup_settings table to include new columns for missing tables
ALTER TABLE database_cleanup_settings
ADD COLUMN IF NOT EXISTS location_alerts_retention_days INTEGER DEFAULT 30,
ADD COLUMN IF NOT EXISTS biometric_credentials_retention_days INTEGER DEFAULT 365,
ADD COLUMN IF NOT EXISTS feedback_submissions_retention_days INTEGER DEFAULT 90,
ADD COLUMN IF NOT EXISTS system_logs_retention_days INTEGER DEFAULT 30,
ADD COLUMN IF NOT EXISTS qr_sessions_retention_days INTEGER DEFAULT 7;

-- Update the selected_data_types to include new table types
UPDATE database_cleanup_settings
SET selected_data_types = jsonb_build_array(
  'notifications',
  'attendance_records',
  'audit_logs',
  'excuses',
  'location_alerts',
  'biometric_credentials',
  'feedback_submissions',
  'system_logs',
  'user_activity_logs',
  'qr_sessions'
)
WHERE selected_data_types IS NOT NULL;

-- Insert default settings if no settings exist
INSERT INTO database_cleanup_settings (
  enabled,
  notifications_retention_days,
  attendance_records_retention_days,
  audit_logs_retention_days,
  excuses_retention_days,
  location_alerts_retention_days,
  biometric_credentials_retention_days,
  feedback_submissions_retention_days,
  system_logs_retention_days,
  user_activity_logs_retention_days,
  qr_sessions_retention_days,
  selected_data_types,
  cleanup_frequency,
  next_cleanup_at
)
SELECT
  false,
  90,
  365,
  180,
  180,
  30,
  365,
  90,
  30,
  90,
  7,
  jsonb_build_array(
    'notifications',
    'attendance_records',
    'audit_logs',
    'excuses',
    'location_alerts',
    'biometric_credentials',
    'feedback_submissions',
    'system_logs',
    'user_activity_logs',
    'qr_sessions'
  ),
  'weekly',
  (now() + interval '7 days')
WHERE NOT EXISTS (
  SELECT 1 FROM database_cleanup_settings
);

-- Create function to get comprehensive cleanup statistics
CREATE OR REPLACE FUNCTION get_cleanup_statistics()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  stats JSONB := '{}';
  table_count INTEGER;
BEGIN
  -- Helper function to safely count table rows
  CREATE OR REPLACE FUNCTION safe_count_table(table_name TEXT)
  RETURNS INTEGER
  LANGUAGE plpgsql
  AS $inner$
  DECLARE
    count_result INTEGER := 0;
  BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = safe_count_table.table_name) THEN
      EXECUTE format('SELECT COUNT(*) FROM %I', table_name) INTO count_result;
    END IF;
    RETURN count_result;
  EXCEPTION WHEN OTHERS THEN
    RETURN 0;
  END;
  $inner$;

  -- Get counts for all tables
  stats := jsonb_build_object(
    'notifications', safe_count_table('notifications'),
    'attendance_records', safe_count_table('attendance_records'),
    'audit_logs', safe_count_table('audit_logs'),
    'excuses', safe_count_table('excuses'),
    'location_alerts', safe_count_table('location_alerts'),
    'biometric_credentials', safe_count_table('biometric_credentials'),
    'feedback_submissions', safe_count_table('feedback_submissions'),
    'system_logs', safe_count_table('system_logs'),
    'user_activity_logs', safe_count_table('user_activity_logs'),
    'qr_sessions', safe_count_table('qr_sessions'),
    'timestamp', now()
  );

  -- Clean up the helper function
  DROP FUNCTION safe_count_table(TEXT);

  RETURN stats;
END;
$$;

-- Grant execute permission on statistics function
GRANT EXECUTE ON FUNCTION get_cleanup_statistics() TO authenticated;
