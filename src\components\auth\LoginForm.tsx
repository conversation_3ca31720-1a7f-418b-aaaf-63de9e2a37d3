import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PasswordInput } from "@/components/ui/password-input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import { AlertCircle, Fingerprint, Mail, Sparkles } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { supabase } from "@/lib/supabase";
import { useTranslation } from "react-i18next";

import { useBranding } from "@/hooks/useBranding";
import BiometricSignIn from "./BiometricSignIn";
import { isWebAuthnAvailable } from "@/lib/webauthn";

export default function LoginForm() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [customLoginMessage, setCustomLoginMessage] = useState<string | null>(
    null
  );
  const [activeTab, setActiveTab] = useState("email");
  const [biometricSupported, setBiometricSupported] = useState(false);

  const { toast } = useToast();
  const { signIn } = useAuth();
  const { uiText } = useBranding();
  const { t } = useTranslation();

  useEffect(() => {
    setBiometricSupported(isWebAuthnAvailable());
  }, []);

  // Function to fetch custom login message
  const fetchCustomLoginMessage = async () => {
    try {
      // Get the school ID from URL if available
      const urlParams = new URLSearchParams(window.location.search);
      const schoolId = urlParams.get("school");

      // Set up headers with school ID if available
      const headers: Record<string, string> = {};
      if (schoolId) {
        headers["x-school-id"] = schoolId;
      }

      // Try the public function that allows anonymous access
      const { data, error } = await supabase.rpc(
        "get_public_login_message",
        {},
        { headers }
      );

      if (!error && data) {
        // Handle different formats of data
        let messageToSet = null;

        if (typeof data === "string") {
          messageToSet = data.trim();
        } else if (typeof data === "object") {
          // It could be { value: "message" } or just the value itself
          if (data.value !== undefined) {
            messageToSet = data.value.trim();
          } else {
            try {
              messageToSet = JSON.stringify(data).trim();
            } catch (e) {
              console.error("Error processing message:", e);
            }
          }
        }

        // Only set the message if it's not empty
        if (messageToSet && messageToSet.length > 0) {
          setCustomLoginMessage(messageToSet);
        } else {
          // If message is empty, set to null to hide the container
          setCustomLoginMessage(null);
        }
        return;
      }

      // No message found or error occurred, don't show any message
      setCustomLoginMessage(null);
    } catch (error) {
      console.error("Error fetching custom login message:", error);
      // Don't show any message on error
      setCustomLoginMessage(null);
    }
  };

  // Fetch custom login message on component mount
  useEffect(() => {
    fetchCustomLoginMessage();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setLoginError(null);

    try {
      await signIn(email, password);
      // Navigation is handled in AuthContext
    } catch (error: any) {
      console.error("Login error:", error);

      // Format error message for better user experience
      let errorMessage = t("auth.loginFailed");

      if (error.message) {
        if (error.message.includes("Invalid login credentials")) {
          errorMessage = t("auth.invalidCredentials");
        } else if (
          error.message.includes("Failed to fetch") ||
          error.message.includes("NetworkError") ||
          error.message.includes("Unexpected end of input")
        ) {
          errorMessage = t("auth.networkError");
        } else {
          // Use the original error message
          errorMessage = error.message;
        }
      }

      // Show specific error in the form
      setLoginError(errorMessage);

      // If it's a network error, suggest refreshing the page
      if (errorMessage.includes(t("auth.networkError"))) {
        setTimeout(() => {
          if (confirm(t("auth.refreshPageConfirm"))) {
            window.location.reload();
          }
        }, 1500);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto overflow-hidden">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl text-primary flex items-center justify-center gap-2">
          {uiText.LOGIN_TITLE}
          {biometricSupported && <Sparkles className="w-5 h-5 text-yellow-500" />}
        </CardTitle>
        <CardDescription>{uiText.LOGIN_SUBTITLE}</CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        {loginError && (
          <div className="px-6 pb-4">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{loginError}</AlertDescription>
            </Alert>
          </div>
        )}

        {customLoginMessage && (
          <div className="px-6 pb-4">
            <div className="p-5 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg text-sm border-l-4 border-primary shadow-md animate-fadeIn">
              <p className="text-primary font-medium leading-relaxed">
                {customLoginMessage}
              </p>
            </div>
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="px-6 mb-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="email" className="flex items-center gap-2">
                <Mail className="w-4 h-4" />
                {t("auth.emailAndPassword")}
              </TabsTrigger>
              <TabsTrigger
                value="biometric"
                className="flex items-center gap-2"
                disabled={!biometricSupported}
              >
                <Fingerprint className="w-4 h-4" />
                {t("auth.biometric")}
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="email" className="px-6 pb-6 mt-0">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">{t("common.email")}</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder={t("auth.placeholders.yourEmail")}
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isLoading}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">{t("common.password")}</Label>
                  <PasswordInput
                    id="password"
                    placeholder={t("auth.placeholders.password")}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    disabled={isLoading}
                    required
                  />
                </div>
                <Button
                  type="submit"
                  className="w-full bg-primary hover:bg-primary-light"
                  disabled={isLoading}
                >
                  {isLoading ? t("auth.loggingIn") : t("auth.login")}
                </Button>
              </form>
            </motion.div>
          </TabsContent>

          <TabsContent value="biometric" className="px-6 pb-6 mt-0">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <BiometricSignIn
                onSuccess={() => {
                  toast({
                    title: t("auth.welcomeBack"),
                    description: t("auth.signedInSuccessfully"),
                  });
                }}
                onError={(error) => {
                  setLoginError(error);
                }}
                className="border-0 shadow-none"
              />
            </motion.div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex flex-col gap-2">
        <div className="text-sm text-center">
          <span className="text-muted-foreground">{t("auth.noAccount")} </span>
          <Link to="/signup" className="text-primary font-medium">
            {t("auth.signUp")}
          </Link>
        </div>
      </CardFooter>
    </Card>
  );
}
