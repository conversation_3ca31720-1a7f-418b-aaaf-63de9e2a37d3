import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { toast as sonnerToast } from "sonner";
import { Loader2, MapPin, AlertCircle, Save } from "lucide-react";
import { supabase } from "@/lib/supabase";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useTranslation } from "react-i18next";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface RoomLocationSettingsProps {
  roomId: string;
}

export function RoomLocationSettings({ roomId }: RoomLocationSettingsProps) {
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [savingRadius, setSavingRadius] = useState(false);
  const [locationPermission, setLocationPermission] =
    useState<PermissionState | null>(null);
  const [location, setLocation] = useState<{
    latitude: number;
    longitude: number;
    radius_meters: number;
  } | null>(null);
  const [localRadius, setLocalRadius] = useState<number | null>(null);
  const [localLatitude, setLocalLatitude] = useState<number | null>(null);
  const [localLongitude, setLocalLongitude] = useState<number | null>(null);
  const [hasUnsavedLocation, setHasUnsavedLocation] = useState(false);
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    checkLocationPermission();
    ensureTableExists();
    fetchRoomLocation();
  }, [roomId]);

  const checkLocationPermission = async () => {
    try {
      // Check if the Permissions API is supported
      if ("permissions" in navigator) {
        const permission = await navigator.permissions.query({
          name: "geolocation",
        });
        setLocationPermission(permission.state);

        // Listen for permission changes
        permission.addEventListener("change", () => {
          setLocationPermission(permission.state);
        });
      } else {
        // Fallback for browsers that don't support the Permissions API
        setLocationPermission("prompt");
      }
    } catch (error) {
      console.error("Error checking location permission:", error);
      setLocationPermission("prompt");
    }
  };

  const ensureTableExists = async () => {
    try {
      // First verify the room exists and teacher has access
      const { data: roomData, error: roomError } = await supabase
        .from("rooms")
        .select("id, teacher_id")
        .eq("id", roomId)
        .single();

      if (roomError) {
        console.error("Error checking room:", roomError);
        toast({
          title: "Setup Error",
          description: "Unable to verify room access. Please try again.",
          variant: "destructive",
        });
        return false;
      }

      if (!roomData) {
        console.error("Room not found:", roomId);
        toast({
          title: "Setup Error",
          description: "Room not found. Please refresh and try again.",
          variant: "destructive",
        });
        return false;
      }

      // Get teacher's profile
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        console.error("No authenticated user found");
        toast({
          title: "Authentication Error",
          description: "Please log in again.",
          variant: "destructive",
        });
        return false;
      }

      // Get teacher's profile ID
      const { data: teacherProfile, error: profileError } = await supabase
        .from("profiles")
        .select("id")
        .eq("user_id", user.id)
        .eq("role", "teacher")
        .single();

      if (profileError || !teacherProfile) {
        console.error("Error getting teacher profile:", profileError);
        toast({
          title: "Access Error",
          description: "Could not verify teacher access. Please try again.",
          variant: "destructive",
        });
        return false;
      }

      // Verify teacher has access to this room
      if (roomData.teacher_id !== teacherProfile.id) {
        console.error("Teacher does not have access to room:", roomId);
        toast({
          title: "Access Error",
          description: "You do not have permission to modify this room.",
          variant: "destructive",
        });
        return false;
      }

      // Just try to select from the table to verify it exists
      const { error } = await supabase
        .from("room_locations")
        .select("id")
        .limit(1);

      if (error) {
        console.error("Error checking table:", error);
        toast({
          title: "Setup Error",
          description:
            "Room locations table is not properly configured. Please contact administrator.",
          variant: "destructive",
        });
        return false;
      }
      return true;
    } catch (error) {
      console.error("Error checking table:", error);
      return false;
    }
  };

  const fetchRoomLocation = async () => {
    if (!roomId) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("room_locations")
        .select("*")
        .eq("room_id", roomId)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          // No location set yet, this is fine
          setLocation(null);
        } else if (error.code === "PGRST204") {
          // Table doesn't exist yet
          console.warn("Room locations table does not exist");
          setLocation(null);
        } else {
          throw error;
        }
      } else if (data) {
        const locationData = {
          latitude: Number(data.latitude),
          longitude: Number(data.longitude),
          radius_meters: data.radius_meters,
        };
        setLocation(locationData);
        setLocalRadius(data.radius_meters);
        setLocalLatitude(Number(data.latitude));
        setLocalLongitude(Number(data.longitude));
        setHasUnsavedLocation(false);
      }
    } catch (error) {
      console.error("Error fetching room location:", error);
      toast({
        title: "Error",
        description: "Failed to fetch room location. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getCurrentLocation = () => {
    return new Promise<GeolocationPosition>((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error("Geolocation is not supported by your browser"));
        return;
      }

      navigator.geolocation.getCurrentPosition(resolve, reject, {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0,
      });
    });
  };

  const handleGetCurrentLocation = async () => {
    try {
      setUpdating(true);
      console.log("Getting current location...");
      const position = await getCurrentLocation();
      console.log("Current position:", position.coords);

      // Set the coordinates in local state for editing (don't save yet)
      setLocalLatitude(position.coords.latitude);
      setLocalLongitude(position.coords.longitude);
      setHasUnsavedLocation(true);

      sonnerToast.success(t("teacher.settings.locationRetrieved"), {
        description: t("teacher.settings.locationRetrievedDescription"),
      });

      // Update permission state after successful location access
      checkLocationPermission();
    } catch (error) {
      console.error("Error getting location:", error);
      let errorMessage = "Failed to get current location";

      if (error instanceof GeolocationPositionError) {
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage =
              "Please allow location access in your browser settings to get current location";
            setLocationPermission("denied");
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage =
              "Location information is unavailable. Please try again";
            break;
          case error.TIMEOUT:
            errorMessage = "Location request timed out. Please try again";
            break;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      sonnerToast.error("Error", {
        description: errorMessage,
      });
    } finally {
      setUpdating(false);
    }
  };

  const handleSaveLocation = async () => {
    if (localLatitude === null || localLongitude === null) return;

    try {
      setUpdating(true);

      // Check if table exists first
      // Verifying table and permissions
      const tableExists = await ensureTableExists();
      if (!tableExists) {
        throw new Error("Room locations table is not properly configured");
      }

      const locationData = {
        room_id: roomId,
        latitude: localLatitude,
        longitude: localLongitude,
        radius_meters: location?.radius_meters || 50,
      };

      // Attempting to save room location

      // Try to insert/update the location with proper conflict handling
      const { error: upsertError } = await supabase
        .from("room_locations")
        .upsert(locationData, {
          onConflict: "room_id",
          ignoreDuplicates: false,
        });

      if (upsertError) {
        console.error("Error upserting location:", upsertError);
        throw upsertError;
      }

      // Verify the location was saved correctly
      console.log("Verifying saved location...");
      const { data: savedLocation, error: verifyError } = await supabase
        .from("room_locations")
        .select("*")
        .eq("room_id", roomId)
        .single();

      if (verifyError) {
        console.error("Error verifying saved location:", verifyError);
        throw new Error("Location was not saved correctly. Please try again.");
      }

      if (!savedLocation) {
        console.error("Location not found after saving");
        throw new Error("Location was not saved correctly. Please try again.");
      }

      // Location saved successfully

      const newRadius = location?.radius_meters || 50;
      setLocation({
        latitude: localLatitude,
        longitude: localLongitude,
        radius_meters: newRadius,
      });
      setLocalRadius(newRadius);
      setHasUnsavedLocation(false);

      sonnerToast.success(t("teacher.settings.locationUpdated"), {
        description: t("teacher.settings.locationUpdatedMessage"),
      });
    } catch (error) {
      console.error("Error saving location:", error);
      let errorMessage = "Failed to save room location";

      if (error instanceof Error) {
        errorMessage = error.message;
      }

      sonnerToast.error("Error", {
        description: errorMessage,
      });
    } finally {
      setUpdating(false);
    }
  };

  const handleSaveRadius = async () => {
    if (!location || localRadius === null) return;

    try {
      setSavingRadius(true);

      const { error } = await supabase
        .from("room_locations")
        .update({ radius_meters: localRadius })
        .eq("room_id", roomId);

      if (error) throw error;

      setLocation({
        ...location,
        radius_meters: localRadius,
      });

      sonnerToast.success(t("teacher.settings.radiusUpdated"), {
        description: t("teacher.settings.radiusUpdatedMessage"),
      });
    } catch (error) {
      console.error("Error updating radius:", error);
      sonnerToast.error(t("common.error"), {
        description: t("teacher.settings.errorUpdatingRadius"),
      });
    } finally {
      setSavingRadius(false);
    }
  };

  // Check if radius has been changed
  const hasRadiusChanged = location && localRadius !== null && localRadius !== location.radius_meters;

  // Check if location coordinates have been changed
  const hasLocationChanged = (localLatitude !== null && localLongitude !== null) &&
    (!location || localLatitude !== location.latitude || localLongitude !== location.longitude);

  // Show location fields if we have coordinates (either from existing location or newly retrieved)
  const showLocationFields = location || (localLatitude !== null && localLongitude !== null);

  const renderLocationPermissionMessage = () => {
    if (locationPermission === "denied") {
      return (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {t("teacher.settings.locationPermissionDenied")}
            <Button
              variant="link"
              className="px-0 text-destructive underline"
              onClick={() =>
                window.open("chrome://settings/content/location", "_blank")
              }
            >
              Open Browser Settings
            </Button>
          </AlertDescription>
        </Alert>
      );
    }

    if (!location) {
      return (
        <Alert className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            No location is set for this room. Click the button below to set the
            current location.
            {locationPermission === "prompt" && (
              <p className="mt-2 text-sm text-muted-foreground">
                You will be asked to allow location access when you click the
                button.
              </p>
            )}
          </AlertDescription>
        </Alert>
      );
    }

    return null;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <LoadingSpinner message={t("loading.roomSettings")} />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("teacher.settings.roomLocationSettings")}</CardTitle>
        <CardDescription>
          {t("teacher.settings.roomLocationDescription")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {renderLocationPermissionMessage()}

        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button
              onClick={handleGetCurrentLocation}
              disabled={updating || locationPermission === "denied"}
              className="flex items-center gap-2"
            >
              {updating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <MapPin className="h-4 w-4" />
              )}
              {location
                ? t("teacher.settings.updateCurrentLocation")
                : t("teacher.settings.setCurrentLocation")}
            </Button>
          </div>

          {showLocationFields && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>{t("teacher.settings.latitude")}</Label>
                  <Input
                    type="number"
                    step="any"
                    value={localLatitude ?? (location?.latitude || "")}
                    onChange={(e) => {
                      setLocalLatitude(Number(e.target.value));
                      setHasUnsavedLocation(true);
                    }}
                    placeholder="Enter latitude"
                  />
                </div>
                <div className="space-y-2">
                  <Label>{t("teacher.settings.longitude")}</Label>
                  <Input
                    type="number"
                    step="any"
                    value={localLongitude ?? (location?.longitude || "")}
                    onChange={(e) => {
                      setLocalLongitude(Number(e.target.value));
                      setHasUnsavedLocation(true);
                    }}
                    placeholder="Enter longitude"
                  />
                </div>
              </div>

              {(hasLocationChanged || hasUnsavedLocation) && (
                <div className="flex gap-2">
                  <Button
                    onClick={handleSaveLocation}
                    disabled={updating || localLatitude === null || localLongitude === null}
                    className="flex items-center gap-2"
                  >
                    {updating ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4" />
                    )}
                    {t("teacher.settings.saveLocation")}
                  </Button>
                  {hasUnsavedLocation && (
                    <Button
                      variant="outline"
                      onClick={() => {
                        setLocalLatitude(location?.latitude || null);
                        setLocalLongitude(location?.longitude || null);
                        setHasUnsavedLocation(false);
                      }}
                    >
                      {t("common.cancel")}
                    </Button>
                  )}
                </div>
              )}
              <div className="space-y-2">
                <Label>{t("teacher.settings.attendanceRadius")}</Label>
                <div className="flex gap-2">
                  <Input
                    type="number"
                    value={localRadius ?? location.radius_meters}
                    onChange={(e) => setLocalRadius(Number(e.target.value))}
                    min={10}
                    max={1000}
                    className="flex-1"
                  />
                  {hasRadiusChanged && (
                    <Button
                      onClick={handleSaveRadius}
                      disabled={savingRadius}
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      {savingRadius ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4" />
                      )}
                      {t("common.save")}
                    </Button>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">
                  {t("teacher.settings.studentsWithinRadiusRoom")}
                </p>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
