import { supabase } from '@/lib/supabase';
import { RealtimeChannel } from '@supabase/supabase-js';

/**
 * Centralized subscription manager to prevent WebSocket connection issues
 * and ensure proper cleanup of realtime subscriptions
 */
class SubscriptionManager {
  private subscriptions = new Map<string, RealtimeChannel>();
  private isInitialized = false;
  private isDev = import.meta.env.DEV;

  /**
   * Initialize the subscription manager
   */
  init() {
    if (this.isInitialized) return;
    
    this.isInitialized = true;
    
    // Clean up all subscriptions when the page is about to unload
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });

    // Clean up subscriptions when the page becomes hidden
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.cleanup();
      }
    });
  }

  /**
   * Create or get an existing subscription
   */
  subscribe(
    key: string,
    channelName: string,
    config: {
      event: string;
      schema: string;
      table: string;
      filter?: string;
    },
    callback: (payload: any) => void
  ): () => void {
    // Clean up existing subscription with the same key
    this.unsubscribe(key);

    try {
      // Create new channel with unique name
      const uniqueChannelName = `${channelName}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      const channel = supabase.channel(uniqueChannelName);

      // Set up the subscription
      channel.on('postgres_changes' as any, config, callback).subscribe();

      // Store the subscription
      this.subscriptions.set(key, channel);

      // Subscription created (logging disabled for cleaner console)

      // Return cleanup function
      return () => this.unsubscribe(key);
    } catch (error) {
      if (this.isDev) {
        console.error(`❌ Failed to create subscription ${key}:`, error);
      }
      return () => {};
    }
  }

  /**
   * Unsubscribe from a specific subscription
   */
  unsubscribe(key: string): void {
    const subscription = this.subscriptions.get(key);
    if (subscription) {
      try {
        // Add a longer delay to allow WebSocket to establish connection first
        // This prevents "WebSocket is closed before connection is established" warnings
        setTimeout(() => {
          try {
            supabase.removeChannel(subscription);
          } catch (error) {
            // Silently handle cleanup errors to avoid console noise
          }
        }, 500);
        this.subscriptions.delete(key);
        // Subscription cleaned up (logging disabled for cleaner console)
      } catch (error) {
        // Silently handle errors to keep console clean
      }
    }
  }

  /**
   * Clean up all subscriptions
   */
  cleanup(): void {
    if (this.isDev && this.subscriptions.size > 0) {
      console.log(`🧹 Cleaning up ${this.subscriptions.size} subscriptions...`);
    }

    for (const [key, subscription] of this.subscriptions) {
      try {
        supabase.removeChannel(subscription);
        if (this.isDev) {
          console.log(`🧹 Cleaned up subscription: ${key}`);
        }
      } catch (error) {
        if (this.isDev) {
          console.warn(`⚠️ Error cleaning up subscription ${key}:`, error);
        }
      }
    }

    this.subscriptions.clear();
    if (this.isDev && this.subscriptions.size === 0) {
      console.log('✅ All subscriptions cleaned up');
    }
  }

  /**
   * Get the number of active subscriptions
   */
  getActiveCount(): number {
    return this.subscriptions.size;
  }

  /**
   * Get all active subscription keys
   */
  getActiveKeys(): string[] {
    return Array.from(this.subscriptions.keys());
  }
}

// Export singleton instance
export const subscriptionManager = new SubscriptionManager();

// Auto-initialize
subscriptionManager.init();
