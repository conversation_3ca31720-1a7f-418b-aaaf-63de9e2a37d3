import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import {
  getFooterSettings,
  updateFooterSettings,
  FooterSettings as FooterSettingsType,
} from "@/lib/api/footer-settings";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import {
  Github,
  Linkedin,
  Twitter,
  Facebook,
  Instagram,
  Youtube,
  Globe,
  Save,
  RefreshCw,
  FileText,
  Code,
  Settings,
  Mail,
  Smartphone,
  Cookie,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

// Define the form schema with Zod
const footerSettingsSchema = z.object({
  developer_name: z.string().min(1, {
    message: "Developer name is required.",
  }),
  developer_website: z.string().url({
    message: "Please enter a valid URL.",
  }),
  contact_email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  app_tagline: z.string(),
  github_url: z
    .string()
    .url({
      message: "Please enter a valid URL.",
    })
    .optional()
    .or(z.literal("")),
  linkedin_url: z
    .string()
    .url({
      message: "Please enter a valid URL.",
    })
    .optional()
    .or(z.literal("")),
  twitter_url: z
    .string()
    .url({
      message: "Please enter a valid URL.",
    })
    .optional()
    .or(z.literal("")),
  facebook_url: z
    .string()
    .url({
      message: "Please enter a valid URL.",
    })
    .optional()
    .or(z.literal("")),
  instagram_url: z
    .string()
    .url({
      message: "Please enter a valid URL.",
    })
    .optional()
    .or(z.literal("")),
  youtube_url: z
    .string()
    .url({
      message: "Please enter a valid URL.",
    })
    .optional()
    .or(z.literal("")),
  whatsapp_url: z
    .string()
    .url({
      message: "Please enter a valid URL.",
    })
    .optional()
    .or(z.literal("")),
  show_github: z.boolean(),
  show_linkedin: z.boolean(),
  show_twitter: z.boolean(),
  show_facebook: z.boolean(),
  show_instagram: z.boolean(),
  show_youtube: z.boolean(),
  show_whatsapp: z.boolean(),
  show_developer_info: z.boolean(),
  show_copyright: z.boolean(),
  show_app_info: z.boolean(),
  show_contact: z.boolean(),
  show_legal: z.boolean(),
  custom_copyright_text: z.string().optional().or(z.literal("")),
  privacy_policy_url: z
    .string()
    .url({
      message: "Please enter a valid URL.",
    })
    .optional()
    .or(z.literal("")),
  terms_of_service_url: z
    .string()
    .url({
      message: "Please enter a valid URL.",
    })
    .optional()
    .or(z.literal("")),
  cookie_policy_url: z
    .string()
    .url({
      message: "Please enter a valid URL.",
    })
    .optional()
    .or(z.literal("")),
});

type FooterSettingsFormValues = z.infer<typeof footerSettingsSchema>;

export default function FooterSettings() {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<FooterSettingsType | null>(null);

  // Initialize form with react-hook-form
  const form = useForm<FooterSettingsFormValues>({
    resolver: zodResolver(footerSettingsSchema),
    defaultValues: {
      developer_name: "Attendance Tracking System Team",
      developer_website: "https://attendancetracking.edu",
      contact_email: "<EMAIL>",
      app_tagline:
        "Streamlining attendance management for educational institutions",
      github_url: "",
      linkedin_url: "",
      twitter_url: "",
      facebook_url: "",
      instagram_url: "",
      youtube_url: "",
      whatsapp_url: "",
      show_github: true,
      show_linkedin: true,
      show_twitter: true,
      show_facebook: true,
      show_instagram: true,
      show_youtube: true,
      show_whatsapp: true,
      show_developer_info: true,
      show_copyright: true,
      show_app_info: true,
      show_contact: true,
      show_legal: true,
      custom_copyright_text: "",
      privacy_policy_url: "",
      terms_of_service_url: "",
      cookie_policy_url: "",
    },
  });

  // Fetch footer settings
  useEffect(() => {
    const fetchSettings = async () => {
      setLoading(true);
      try {
        const data = await getFooterSettings();
        if (data) {
          setSettings(data);
          // Update form values
          form.reset({
            developer_name: data.developer_name,
            developer_website: data.developer_website,
            contact_email: data.contact_email,
            app_tagline: data.app_tagline,
            github_url: data.github_url || "",
            linkedin_url: data.linkedin_url || "",
            twitter_url: data.twitter_url || "",
            facebook_url: data.facebook_url || "",
            instagram_url: data.instagram_url || "",
            youtube_url: data.youtube_url || "",
            whatsapp_url: data.whatsapp_url || "",
            show_github: data.show_github,
            show_linkedin: data.show_linkedin,
            show_twitter: data.show_twitter,
            show_facebook: data.show_facebook,
            show_instagram: data.show_instagram,
            show_youtube: data.show_youtube,
            show_whatsapp: data.show_whatsapp,
            show_developer_info: data.show_developer_info,
            show_copyright: data.show_copyright,
            show_app_info: data.show_app_info,
            show_contact: data.show_contact,
            show_legal: data.show_legal,
            custom_copyright_text: data.custom_copyright_text || "",
            privacy_policy_url: data.privacy_policy_url || "",
            terms_of_service_url: data.terms_of_service_url || "",
            cookie_policy_url: data.cookie_policy_url || "",
          });
        }
      } catch (error) {
        console.error("Error fetching footer settings:", error);
        toast.error(t("Error"), {
          description: t("Failed to load footer settings."),
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [form, t]);

  // Handle form submission
  const onSubmit = async (values: FooterSettingsFormValues) => {
    setSaving(true);
    try {
      const result = await updateFooterSettings(values);

      if (result) {
        setSettings(result);
        toast.success(t("Success"), {
          description: t("Footer settings updated successfully."),
          duration: 3000,
        });
      } else {
        // Failed to update footer settings
        toast.error(t("Error"), {
          description: t("Failed to update footer settings."),
          duration: 5000,
        });
      }
    } catch (error) {
      console.error("Error updating footer settings:", error);
      console.error(
        "Error details:",
        error instanceof Error ? error.message : JSON.stringify(error)
      );
      toast.error(t("Error"), {
        description: t("An unexpected error occurred."),
        duration: 5000,
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            {t("Footer Settings")}
          </CardTitle>
          <CardDescription>
            {t("Customize the footer of your application.")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          {t("Footer Settings")}
        </CardTitle>
        <CardDescription>
          {t("Customize the footer of your application.")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs defaultValue="app" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="app" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  {t("App Info")}
                </TabsTrigger>
                <TabsTrigger
                  value="developer"
                  className="flex items-center gap-2"
                >
                  <Code className="h-4 w-4" />
                  {t("Developer Info")}
                </TabsTrigger>
                <TabsTrigger value="social" className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  {t("Social Media")}
                </TabsTrigger>
                <TabsTrigger value="legal" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  {t("Legal & Copyright")}
                </TabsTrigger>
              </TabsList>

              {/* App Info Tab */}
              <TabsContent value="app" className="space-y-4 pt-4">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>{t("App Information")}</AlertTitle>
                  <AlertDescription>
                    {t(
                      "Configure the app information displayed in the footer."
                    )}
                  </AlertDescription>
                </Alert>

                <FormField
                  control={form.control}
                  name="show_app_info"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">
                          {t("Show App Information")}
                        </FormLabel>
                        <FormDescription>
                          {t("Display app logo and tagline in the footer.")}
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="app_tagline"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("App Tagline")}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Streamlining attendance management for educational institutions"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {t("A brief description of your application.")}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="show_contact"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">
                          {t("Show Contact Information")}
                        </FormLabel>
                        <FormDescription>
                          {t("Display contact information in the footer.")}
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contact_email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("Contact Email")}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          type="email"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {t(
                          "The email address displayed in the footer contact section."
                        )}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              {/* Developer Info Tab */}
              <TabsContent value="developer" className="space-y-4 pt-4">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>{t("Developer Information")}</AlertTitle>
                  <AlertDescription>
                    {t(
                      "This information will be displayed in the footer of your application."
                    )}
                  </AlertDescription>
                </Alert>

                <FormField
                  control={form.control}
                  name="show_developer_info"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">
                          {t("Show Developer Information")}
                        </FormLabel>
                        <FormDescription>
                          {t("Display developer information in the footer.")}
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="developer_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("Developer Name")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Attendance Tracking System Team"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          {t("The name of the developer or team.")}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="developer_website"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("Developer Website")}</FormLabel>
                        <FormControl>
                          <Input placeholder="https://example.com" {...field} />
                        </FormControl>
                        <FormDescription>
                          {t("The website URL of the developer or team.")}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              {/* Social Media Tab */}
              <TabsContent value="social" className="space-y-4 pt-4">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>{t("Social Media Links")}</AlertTitle>
                  <AlertDescription>
                    {t(
                      "Add social media links to the footer. Leave empty to hide a specific platform."
                    )}
                  </AlertDescription>
                </Alert>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>{t("Social Media Visibility")}</AlertTitle>
                  <AlertDescription>
                    {t(
                      "Control which social media platforms are displayed in the footer."
                    )}
                  </AlertDescription>
                </Alert>

                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="show_github"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="flex items-center gap-2">
                          <Github className="h-4 w-4" />
                          <FormLabel className="m-0">GitHub</FormLabel>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="show_linkedin"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="flex items-center gap-2">
                          <Linkedin className="h-4 w-4" />
                          <FormLabel className="m-0">LinkedIn</FormLabel>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="show_twitter"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="flex items-center gap-2">
                          <Twitter className="h-4 w-4" />
                          <FormLabel className="m-0">Twitter</FormLabel>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="show_facebook"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="flex items-center gap-2">
                          <Facebook className="h-4 w-4" />
                          <FormLabel className="m-0">Facebook</FormLabel>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="show_instagram"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="flex items-center gap-2">
                          <Instagram className="h-4 w-4" />
                          <FormLabel className="m-0">Instagram</FormLabel>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="show_youtube"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="flex items-center gap-2">
                          <Youtube className="h-4 w-4" />
                          <FormLabel className="m-0">YouTube</FormLabel>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="show_whatsapp"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="flex items-center gap-2">
                          <Smartphone className="h-4 w-4" />
                          <FormLabel className="m-0">WhatsApp</FormLabel>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="github_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <Github className="h-4 w-4" />
                          GitHub
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://github.com/username"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="linkedin_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <Linkedin className="h-4 w-4" />
                          LinkedIn
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://linkedin.com/in/username"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="twitter_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <Twitter className="h-4 w-4" />
                          Twitter
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://twitter.com/username"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="facebook_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <Facebook className="h-4 w-4" />
                          Facebook
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://facebook.com/username"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="instagram_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <Instagram className="h-4 w-4" />
                          Instagram
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://instagram.com/username"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="youtube_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <Youtube className="h-4 w-4" />
                          YouTube
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://youtube.com/@channel"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="whatsapp_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <Smartphone className="h-4 w-4" />
                          WhatsApp
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://wa.me/1234567890"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormDescription>
                          {t(
                            "Use format: https://wa.me/[your number with country code]"
                          )}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              {/* Legal & Copyright Tab */}
              <TabsContent value="legal" className="space-y-4 pt-4">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>{t("Legal Information")}</AlertTitle>
                  <AlertDescription>
                    {t(
                      "Add legal information and copyright text to the footer."
                    )}
                  </AlertDescription>
                </Alert>

                <FormField
                  control={form.control}
                  name="show_copyright"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">
                          {t("Show Copyright")}
                        </FormLabel>
                        <FormDescription>
                          {t("Display copyright information in the footer.")}
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="custom_copyright_text"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("Custom Copyright Text")}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="© 2023 Campus Guardian. All rights reserved."
                          {...field}
                          value={field.value || ""}
                        />
                      </FormControl>
                      <FormDescription>
                        {t("Leave empty to use the default copyright text.")}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="show_legal"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">
                          {t("Show Legal Links")}
                        </FormLabel>
                        <FormDescription>
                          {t("Display legal links in the footer.")}
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="privacy_policy_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("Privacy Policy URL")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://example.com/privacy"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormDescription>
                          {t("Link to your privacy policy.")}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="terms_of_service_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("Terms of Service URL")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://example.com/terms"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormDescription>
                          {t("Link to your terms of service.")}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="cookie_policy_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("Cookie Policy URL")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://example.com/cookies"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormDescription>
                          {t("Link to your cookie policy.")}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>
            </Tabs>

            <Separator />

            <Button
              type="submit"
              disabled={saving}
              className="w-full sm:w-auto"
            >
              {saving ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  {t("Saving...")}
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {t("Save Settings")}
                </>
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
