import { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';
import { startAuthentication, isWebAuthnAvailable } from '@/lib/webauthn';

interface BiometricAuthResult {
  success: boolean;
  error?: string;
}

export function useBiometricAuth() {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const authenticateWithBiometrics = async (email: string): Promise<BiometricAuthResult> => {
    try {
      setIsLoading(true);

      // Check if WebAuthn is available
      if (!isWebAuthnAvailable()) {
        return {
          success: false,
          error: 'Biometric authentication is not supported on this device or browser'
        };
      }

      // Get user profile using RPC to bypass RLS
      const { data: profileData, error: profileError } = await supabase.rpc('get_user_for_biometric_auth', {
        user_email: email
      });

      if (profileError) {
        return {
          success: false,
          error: 'Failed to fetch user profile'
        };
      }

      if (!profileData) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      if (!profileData.biometric_registered) {
        return {
          success: false,
          error: 'Biometric authentication not set up for this account'
        };
      }

      // Perform WebAuthn authentication
      await startAuthentication(profileData.user_id);

      // Create a magic link session for the verified user
      const { data, error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: false,
          data: {
            biometric_auth: true,
            user_id: profileData.user_id,
            timestamp: Date.now(),
            verified: true // Mark as pre-verified since biometric auth succeeded
          }
        }
      });

      if (error) {
        console.error('Magic link creation error:', error);
        return {
          success: false,
          error: 'Failed to create authentication session. Please try again.'
        };
      }

      return {
        success: true
      };

    } catch (error: any) {
      console.error('Biometric authentication error:', error);
      
      let errorMessage = 'Biometric authentication failed';
      if (error.message.includes('User cancelled') || error.message.includes('cancelled')) {
        errorMessage = 'Biometric authentication was cancelled';
      } else if (error.message.includes('not found')) {
        errorMessage = 'No biometric credentials found. Please register your biometrics first.';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Biometric authentication timed out. Please try again.';
      }

      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setIsLoading(false);
    }
  };

  const checkBiometricAvailability = async (email: string): Promise<boolean> => {
    try {
      if (!email || !email.includes('@')) {
        return false;
      }

      if (!isWebAuthnAvailable()) {
        return false;
      }

      const { data: profileData, error } = await supabase.rpc('get_user_for_biometric_auth', {
        user_email: email
      });

      if (error) {
        console.error('Error fetching user profile for biometric check:', error);
        return false;
      }

      return profileData && profileData.biometric_registered === true;
    } catch (error) {
      console.error('Error checking biometric availability:', error);
      return false;
    }
  };

  return {
    authenticateWithBiometrics,
    checkBiometricAvailability,
    isLoading
  };
}
