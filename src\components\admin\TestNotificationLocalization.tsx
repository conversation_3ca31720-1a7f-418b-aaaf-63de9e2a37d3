/**
 * 🧪 Test Component for Notification Localization
 * =====================================================
 * This component allows admins to test the notification localization system
 */

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { createLocalizedNotification } from "@/lib/utils/notification-localization";
import { supabase } from "@/lib/supabase";

export default function TestNotificationLocalization() {
  const [studentId, setStudentId] = useState("");
  const [templateKey, setTemplateKey] = useState<string>("attendanceReminder");
  const [teacherName, setTeacherName] = useState("Test Teacher");
  const [roomName, setRoomName] = useState("Test Room");
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const templateOptions = [
    { value: "attendanceReminder", label: "Attendance Reminder" },
    { value: "attendanceRecorded", label: "Attendance Recorded" },
    { value: "markedPresent", label: "Marked Present" },
    { value: "markedAbsent", label: "Marked Absent" },
    { value: "markedLate", label: "Marked Late" },
    { value: "markedExcused", label: "Marked Excused" },
    { value: "markedPresentByAdmin", label: "Marked Present by Admin" },
    { value: "markedAbsentByAdmin", label: "Marked Absent by Admin" },
    { value: "excuseSubmitted", label: "Excuse Submitted" },
    { value: "excuseApproved", label: "Excuse Approved" },
    { value: "excuseRejected", label: "Excuse Rejected" },
    { value: "excuseExpired", label: "Excuse Expired" },
    { value: "locationAlert", label: "Location Alert" },
    { value: "securityAlert", label: "Security Alert" },
    { value: "systemMaintenance", label: "System Maintenance" },
    { value: "accountUpdate", label: "Account Update" },
    { value: "passwordChanged", label: "Password Changed" },
  ];

  const handleSendTestNotification = async () => {
    if (!studentId.trim()) {
      toast({
        title: "Error",
        description: "Please enter a student ID",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      // Get template parameters based on selected template
      let templateParams: any[] = [];
      let notificationType: 'attendance' | 'absence' | 'system' | 'distance_alert' | 'late' | 'excused' = 'system';

      switch (templateKey) {
        case "attendanceReminder":
          templateParams = [teacherName, roomName];
          notificationType = 'attendance';
          break;
        case "attendanceRecorded":
          templateParams = [roomName, new Date().toLocaleTimeString()];
          notificationType = 'attendance';
          break;
        case "markedPresent":
        case "markedPresentByAdmin":
          templateParams = [roomName, teacherName];
          notificationType = 'attendance';
          break;
        case "markedAbsent":
        case "markedAbsentByAdmin":
          templateParams = [roomName, teacherName];
          notificationType = 'absence';
          break;
        case "markedLate":
          templateParams = [roomName, teacherName];
          notificationType = 'late';
          break;
        case "markedExcused":
          templateParams = [roomName, teacherName];
          notificationType = 'excused';
          break;
        case "excuseSubmitted":
        case "excuseApproved":
        case "excuseRejected":
          templateParams = ["2024-06-14", "2024-06-15", "Medical appointment"];
          notificationType = templateKey === "excuseApproved" ? 'excused' : 'system';
          break;
        case "excuseExpired":
          templateParams = ["2024-06-14", "2024-06-15"];
          notificationType = 'system';
          break;
        case "locationAlert":
          templateParams = [50, roomName]; // 50 meters distance
          notificationType = 'distance_alert';
          break;
        case "securityAlert":
          templateParams = ["Suspicious activity", "Multiple login attempts detected"];
          notificationType = 'system';
          break;
        case "systemMaintenance":
          templateParams = ["02:00", "04:00"];
          notificationType = 'system';
          break;
        case "accountUpdate":
          templateParams = ["Profile information updated"];
          notificationType = 'system';
          break;
        case "passwordChanged":
          templateParams = [];
          notificationType = 'system';
          break;
      }

      const result = await createLocalizedNotification({
        studentId: studentId.trim(),
        type: notificationType,
        templateKey: templateKey as any,
        templateParams,
        metadata: {
          test_notification: true,
          template_used: templateKey,
          sent_at: new Date().toISOString(),
        },
        roomNumber: roomName,
      });

      if (result.success) {
        toast({
          title: "Success",
          description: "Test notification sent successfully! Check the student's notifications tab.",
        });
      } else {
        throw new Error(result.error?.message || "Failed to send notification");
      }
    } catch (error) {
      console.error("Error sending test notification:", error);
      toast({
        title: "Error",
        description: `Failed to send test notification: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCheckStudentLanguage = async () => {
    if (!studentId.trim()) {
      toast({
        title: "Error",
        description: "Please enter a student ID",
        variant: "destructive",
      });
      return;
    }

    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("name, preferred_language")
        .eq("user_id", studentId.trim())
        .single();

      if (error || !data) {
        toast({
          title: "Error",
          description: "Student not found or error fetching data",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Student Language Info",
        description: `${data.name}: Language preference is "${data.preferred_language || 'en'}"`,
      });
    } catch (error) {
      console.error("Error checking student language:", error);
      toast({
        title: "Error",
        description: "Failed to check student language preference",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>🧪 Test Notification Localization</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="studentId">Student ID</Label>
          <Input
            id="studentId"
            placeholder="Enter student user ID"
            value={studentId}
            onChange={(e) => setStudentId(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="template">Notification Template</Label>
          <Select value={templateKey} onValueChange={setTemplateKey}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {templateOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="teacherName">Teacher Name</Label>
            <Input
              id="teacherName"
              value={teacherName}
              onChange={(e) => setTeacherName(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="roomName">Room Name</Label>
            <Input
              id="roomName"
              value={roomName}
              onChange={(e) => setRoomName(e.target.value)}
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={handleSendTestNotification}
            disabled={loading}
            className="flex-1"
          >
            {loading ? "Sending..." : "Send Test Notification"}
          </Button>
          <Button
            onClick={handleCheckStudentLanguage}
            variant="outline"
          >
            Check Student Language
          </Button>
        </div>

        <div className="text-sm text-muted-foreground">
          <p><strong>How to test:</strong></p>
          <ol className="list-decimal list-inside space-y-1">
            <li>Enter a valid student user ID</li>
            <li>Select a notification template</li>
            <li>Click "Check Student Language" to see their preference</li>
            <li>Click "Send Test Notification" to send a localized notification</li>
            <li>Check the student's notifications tab to see the result</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
}
