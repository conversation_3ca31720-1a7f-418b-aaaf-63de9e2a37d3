import { createRoot } from "react-dom/client";
import App from "./App.tsx";
import "./index.css";
import * as serviceWorkerRegistration from "./utils/serviceWorkerRegistration";

// Suppress development-only console messages for cleaner production console
const originalConsoleLog = console.log;
console.log = (...args) => {
  const message = args.join(' ');
  // Filter out development-only messages
  const isDevelopmentMessage =
    message.includes('React DevTools') ||
    message.includes('Download the React DevTools') ||
    message.includes('Fetching settings for room:') ||
    message.includes('Teacher permissions:') ||
    message.includes('Settings found:') ||
    message.includes('✅ Subscription created:') ||
    message.includes('🧹 Subscription cleaned up:') ||
    message.includes('Image URL being set:') ||
    message.includes('Getting current location...') ||
    message.includes('Current position:') ||
    message.includes('Verifying table and permissions...') ||
    message.includes('Table exists and teacher has access') ||
    message.includes('Table exists and admin has access') ||
    message.includes('Saving location data:') ||
    message.includes('Location saved successfully:') ||
    message.includes('Saving radius:') ||
    message.includes('Update result:') ||
    message.includes('Attempting to save room location:') ||
    message.includes('Verifying saved location...') ||
    message.includes('Saving settings:') ||
    message.includes('Saving block settings for block:') ||
    message.includes('Saving room settings for room:') ||
    message.includes('No admin profiles found to migrate') ||
    message.includes('Admin dashboard tab is now visible') ||
    message.includes('AdminStudentDirectory:') ||
    message.includes('No students in state, fetching') ||
    message.includes('Fetching students...') ||
    message.includes('Raw student profiles:') ||
    message.includes('Processing student:') ||
    message.includes('Processed student profiles:') ||
    message.includes('Invitation code state changed to:') ||
    message.includes('Current school data in useEffect:') ||
    message.includes('Invitation code from currentSchool:') ||
    message.includes('Setting invitation code from school data:') ||
    message.includes('Branding data query result:') ||
    message.includes('Loaded branding data from school_branding table:') ||
    message.includes('Location verification settings table exists') ||
    message.includes('Fetched blocks for QR Generator:') ||
    message.includes('Fetched rooms for QR Generator:') ||
    message.includes('🚀 Initializing Automatic QR Service') ||
    message.includes('🤖 Initializing Automatic QR Generation') ||
    message.includes('🔍 Checking attendance time ranges') ||
    message.includes('📋 Found') ||
    message.includes('✅ Automatic QR Service initialized') ||
    message.includes('Email config loaded from database') ||
    message.includes('SMS config loaded from database') ||
    message.includes('Email config loaded from localStorage') ||
    message.includes('SMS config loaded from localStorage') ||
    message.includes('🔔 Automated Reminder Service started') ||
    message.includes('🔔 Automated Reminder Service stopped') ||
    message.includes('🧹 Cleaning up') ||
    message.includes('🧹 Cleaned up subscription:') ||
    message.includes('✅ All subscriptions cleaned up') ||
    message.includes('Created localized notification') ||
    message.includes('Bulk notification creation:') ||
    message.includes('Deleting block') ||
    message.includes('Starting deletion of room:') ||
    message.includes('Successfully deleted room:') ||
    message.includes('Deleting block-level references') ||
    message.includes('Updated profiles to remove block assignment') ||
    message.includes('Deleted location verification settings') ||
    message.includes('Deleted attendance records') ||
    message.includes('Deleted QR sessions') ||
    message.includes('📊 Found') ||
    message.includes('📊 Created') ||
    message.includes('📊 Fetching') ||
    message.includes('🎯 ScanFeedback') ||
    message.includes('🎯 Setting display scans') ||
    message.includes('🔄 recentScans state changed') ||
    message.includes('🔍 Fetching latest QR code') ||
    message.includes('📅 Found QR with expiry') ||
    message.includes('⏱️ Time until new expiry') ||
    message.includes('⏰ Fetched QR code has already expired') ||
    message.includes('attendance records for today') ||
    message.includes('recent scans') ||
    message.includes('scan events') ||
    message.includes('Running database migrations') ||
    message.includes('Checking schools table columns') ||
    message.includes('Schools table columns updated successfully') ||
    message.includes('Creating system_school_settings_overrides table') ||
    message.includes('System school settings overrides table created successfully') ||
    message.includes('Running system admin code migration') ||
    message.includes('System_settings table exists') ||
    message.includes('System admin code upserted successfully') ||
    message.includes('Running database cleanup settings migration') ||
    message.includes('Database cleanup settings table already exists') ||
    message.includes('Running table exists function migration') ||
    message.includes('Table exists function migration completed successfully') ||
    message.includes('Running feedback system migration') ||
    message.includes('Feedback system migration completed successfully') ||
    message.includes('Database migrations completed') ||
    message.includes('Fetching users from database') ||
    message.includes('Raw data from database:') ||
    message.includes('User ') && message.includes('profile_completed=') ||
    message.includes('Transformed users:') ||
    message.includes('Successfully retrieved system admin code from database') ||
    message.includes('Running database cleanup settings migration (direct SQL)') ||
    message.includes('Error fetching footer settings:') ||
    message.includes('Error details:') ||
    message.includes('No footer settings found, returning default settings') ||
    message.includes('Could not find element') ||
    message.includes('DataStore.get: namespace is required') ||
    message.includes('ErrorUtils caught an error') ||
    message.includes('DOMSubtreeModified') ||
    message.includes('Facebook XFBML parse error') ||
    message.includes('Failed to load Facebook SDK') ||
    message.includes('X-Frame-Options') ||
    message.includes('Refused to display');

  if (!isDevelopmentMessage) {
    originalConsoleLog.apply(console, args);
  }
};

createRoot(document.getElementById("root")!).render(<App />);

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://cra.link/PWA
serviceWorkerRegistration.register({
  onUpdate: (registration) => {
    // When new content is available, show a notification
    if (registration && registration.waiting) {
      // Show a toast notification
      if (window.confirm("New version available! Update now?")) {
        registration.waiting.postMessage({ type: "SKIP_WAITING" });
        window.location.reload();
      }
    }
  },
});
