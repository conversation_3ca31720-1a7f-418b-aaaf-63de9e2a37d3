# Attendance Tracking System

## Project Overview

A comprehensive attendance tracking system built for educational institutions, featuring real-time attendance monitoring, parent notifications, and multi-language support.

## Development Setup

### Prerequisites

- Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- Git for version control

### Local Development

Follow these steps:

```sh
# Step 1: Clone the repository
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory
cd attendance-tracking-system

# Step 3: Install dependencies
npm install

# Step 4: Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Step 5: Start the development server
npm run dev
```

### Available Scripts

```sh
npm run dev          # Start development server
npm run build        # Build for production
npm run build:dev    # Build for development
npm run preview      # Preview production build
npm run lint         # Run ESLint
```

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- Supabase (Authentication, Database)
- SendGrid (Email notifications)
- <PERSON><PERSON><PERSON> (SMS notifications)

## Deployment

### Production Build

```sh
npm run build
```

### Environment Configuration

Make sure to set up your production environment variables:

- `VITE_SUPABASE_URL` - Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY` - Your Supabase anonymous key
- `VITE_SENDGRID_API_KEY` - SendGrid API key for emails
- `VITE_TWILIO_*` - Twilio credentials for SMS

### Hosting Options

This project can be deployed to any static hosting service:

- Vercel
- Netlify
- GitHub Pages
- AWS S3 + CloudFront
- Any other static hosting provider

## Email and SMS Notifications

This project includes real email and SMS notifications for parents. To configure these services:

1. **Email Notifications (SendGrid)**

   - Create a [SendGrid](https://sendgrid.com/) account
   - Generate an API key with mail send permissions
   - Verify a sender email address in SendGrid
   - Add these credentials in the Admin > Parent Notifications > Email & SMS Services tab

2. **SMS Notifications (Twilio)**
   - Create a [Twilio](https://www.twilio.com/) account
   - Get your Account SID, Auth Token, and a Twilio phone number
   - Add these credentials in the Admin > Parent Notifications > Email & SMS Services tab
   - For production use, deploy the Supabase Edge Function in `supabase/functions/send-sms/` to handle SMS sending securely

The system will securely store these credentials in the database and use them to send real notifications to parents when students submit absence excuses or when teachers approve/reject them.
