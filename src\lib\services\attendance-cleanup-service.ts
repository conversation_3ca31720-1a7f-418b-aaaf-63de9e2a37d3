import { supabase } from "@/lib/supabase";
import { format, subDays } from "date-fns";

/**
 * Attendance Cleanup Service
 * Automatically removes attendance records older than 7 days
 */
export class AttendanceCleanupService {
  private static instance: AttendanceCleanupService;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  private constructor() {}

  public static getInstance(): AttendanceCleanupService {
    if (!AttendanceCleanupService.instance) {
      AttendanceCleanupService.instance = new AttendanceCleanupService();
    }
    return AttendanceCleanupService.instance;
  }

  /**
   * Start the automatic cleanup service
   * Runs cleanup every 24 hours
   */
  public start(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    
    // Run initial cleanup
    this.performCleanup();

    // Schedule cleanup to run every 24 hours (86400000 ms)
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, 24 * 60 * 60 * 1000);
  }

  /**
   * Stop the automatic cleanup service
   */
  public stop(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.isRunning = false;
  }

  /**
   * Manually trigger cleanup
   */
  public async manualCleanup(): Promise<{ success: boolean; deletedCount: number; error?: string }> {
    return await this.performCleanup();
  }

  /**
   * Perform the actual cleanup operation
   */
  private async performCleanup(): Promise<{ success: boolean; deletedCount: number; error?: string }> {
    try {
      // Calculate the cutoff date (7 days ago)
      const cutoffDate = format(subDays(new Date(), 7), 'yyyy-MM-dd');
      
      // Delete attendance records older than 7 days
      const { data, error, count } = await supabase
        .from("attendance_records")
        .delete({ count: 'exact' })
        .lt("timestamp", cutoffDate + "T00:00:00");

      if (error) {
        console.error("Attendance cleanup error:", error);
        return {
          success: false,
          deletedCount: 0,
          error: error.message,
        };
      }

      const deletedCount = count || 0;
      
      if (deletedCount > 0) {
        // Log successful cleanup (only in development)
        if (process.env.NODE_ENV === 'development') {
          console.log(`Attendance cleanup: Removed ${deletedCount} records older than ${cutoffDate}`);
        }
      }

      return {
        success: true,
        deletedCount,
      };
    } catch (error) {
      console.error("Unexpected error during attendance cleanup:", error);
      return {
        success: false,
        deletedCount: 0,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Get cleanup status
   */
  public getStatus(): { isRunning: boolean; nextCleanup?: Date } {
    return {
      isRunning: this.isRunning,
      nextCleanup: this.isRunning ? new Date(Date.now() + 24 * 60 * 60 * 1000) : undefined,
    };
  }

  /**
   * Clean up records for a specific school
   */
  public async cleanupForSchool(schoolId: string): Promise<{ success: boolean; deletedCount: number; error?: string }> {
    try {
      const cutoffDate = format(subDays(new Date(), 7), 'yyyy-MM-dd');
      
      const { data, error, count } = await supabase
        .from("attendance_records")
        .delete({ count: 'exact' })
        .eq("school_id", schoolId)
        .lt("timestamp", cutoffDate + "T00:00:00");

      if (error) {
        return {
          success: false,
          deletedCount: 0,
          error: error.message,
        };
      }

      return {
        success: true,
        deletedCount: count || 0,
      };
    } catch (error) {
      return {
        success: false,
        deletedCount: 0,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Get records count for the last 7 days
   */
  public async getRecordsCount(schoolId?: string): Promise<{ 
    totalRecords: number; 
    recordsToDelete: number; 
    error?: string 
  }> {
    try {
      const cutoffDate = format(subDays(new Date(), 7), 'yyyy-MM-dd');
      
      // Count total records
      let totalQuery = supabase
        .from("attendance_records")
        .select("*", { count: 'exact', head: true });
      
      if (schoolId) {
        totalQuery = totalQuery.eq("school_id", schoolId);
      }

      const { count: totalRecords, error: totalError } = await totalQuery;

      if (totalError) {
        return {
          totalRecords: 0,
          recordsToDelete: 0,
          error: totalError.message,
        };
      }

      // Count records to delete
      let deleteQuery = supabase
        .from("attendance_records")
        .select("*", { count: 'exact', head: true })
        .lt("timestamp", cutoffDate + "T00:00:00");
      
      if (schoolId) {
        deleteQuery = deleteQuery.eq("school_id", schoolId);
      }

      const { count: recordsToDelete, error: deleteError } = await deleteQuery;

      if (deleteError) {
        return {
          totalRecords: totalRecords || 0,
          recordsToDelete: 0,
          error: deleteError.message,
        };
      }

      return {
        totalRecords: totalRecords || 0,
        recordsToDelete: recordsToDelete || 0,
      };
    } catch (error) {
      return {
        totalRecords: 0,
        recordsToDelete: 0,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
}

// Export singleton instance
export const attendanceCleanupService = AttendanceCleanupService.getInstance();

// Auto-start the service in production
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  attendanceCleanupService.start();
}
