/**
 * 🤖 Automatic QR Status Component
 * 
 * Displays the status of automatic QR generation and provides controls
 * for admins to monitor and manage the service.
 */

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON>ton } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Bot, 
  Play, 
  Square, 
  RefreshCw, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  Activity,
  Timer,
  Building
} from "lucide-react";
import { useAutomaticQR } from "@/hooks/useAutomaticQR";
import { getQRExpirySeconds } from "@/lib/services/qr-security-api";
import { formatDistanceToNow } from "date-fns";
import { tr, enUS } from "date-fns/locale";
import { useTranslation } from "react-i18next";

export default function AutomaticQRStatus() {
  const { t, i18n } = useTranslation();
  const locale = i18n.language === 'tr' ? tr : enUS;
  const {
    status,
    sessions,
    loading,
    currentSchoolSession,
    isCurrentSchoolActive,
    initializeService,
    manualTrigger,
    stopService,
    updateStatus,
    isServiceRunning,
    hasActiveSessions,
  } = useAutomaticQR();

  const getStatusIcon = () => {
    if (loading) return <RefreshCw className="w-5 h-5 animate-spin" />;
    if (status.error) return <XCircle className="w-5 h-5 text-red-500" />;
    if (isServiceRunning) return <CheckCircle className="w-5 h-5 text-green-500" />;
    return <Bot className="w-5 h-5 text-gray-400" />;
  };

  const getStatusText = () => {
    if (loading) return t("admin.automaticQR.initializing");
    if (status.error) return t("admin.automaticQR.error");
    if (isServiceRunning) return t("admin.automaticQR.active");
    return t("admin.automaticQR.inactive");
  };

  const getStatusColor = () => {
    if (loading) return "bg-blue-50 text-blue-700 border-blue-200";
    if (status.error) return "bg-red-50 text-red-700 border-red-200";
    if (isServiceRunning) return "bg-green-50 text-green-700 border-green-200";
    return "bg-gray-50 text-gray-700 border-gray-200";
  };

  return (
    <div className="space-y-6">
      {/* Main Status Card */}
      <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="w-6 h-6 text-blue-600" />
            {t("admin.automaticQR.title")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Status Overview */}
          <div className="flex flex-col sm:flex-row sm:items-center justify-between p-3 sm:p-4 border rounded-lg gap-3">
            <div className="flex items-center gap-3 flex-1 min-w-0">
              {getStatusIcon()}
              <div className="min-w-0 flex-1">
                <h3 className="font-medium text-sm sm:text-base">{t("admin.automaticQR.serviceStatus")}</h3>
                <p className="text-xs sm:text-sm text-gray-600">
                  {getStatusText()}
                  {status.lastUpdate && (
                    <span className="ml-2 block sm:inline">
                      ({t("admin.automaticQR.updated")} {formatDistanceToNow(status.lastUpdate, { addSuffix: true, locale })})
                    </span>
                  )}
                </p>
              </div>
            </div>
            <Badge className={`${getStatusColor()} self-start sm:self-center text-xs`}>
              {getStatusText()}
            </Badge>
          </div>

          {/* Error Display */}
          {status.error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-700">
                {status.error}
              </AlertDescription>
            </Alert>
          )}

          {/* Active Sessions Info */}
          {hasActiveSessions && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
                <Activity className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-blue-900">Active Sessions</p>
                  <p className="text-lg font-bold text-blue-700">{status.activeSessions}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg">
                <Building className="w-5 h-5 text-green-600" />
                <div>
                  <p className="text-sm font-medium text-green-900">Total Rooms</p>
                  <p className="text-lg font-bold text-green-700">
                    {sessions.reduce((total, session) => total + session.roomCount, 0)}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-2 p-3 bg-purple-50 rounded-lg">
                <Timer className="w-5 h-5 text-purple-600" />
                <div>
                  <p className="text-sm font-medium text-purple-900">Current School</p>
                  <p className="text-lg font-bold text-purple-700">
                    {isCurrentSchoolActive ? "Active" : "Inactive"}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Current School Session Details */}
          {currentSchoolSession && (
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-900 mb-2">Your School Session</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-blue-700">Status:</span>
                  <Badge className={currentSchoolSession.isActive ? "ml-2 bg-green-100 text-green-800" : "ml-2 bg-gray-100 text-gray-800"}>
                    {currentSchoolSession.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <div>
                  <span className="text-blue-700">Rooms:</span>
                  <span className="ml-2 font-medium">{currentSchoolSession.rooms.length}</span>
                </div>
                <div>
                  <span className="text-blue-700">Start Time:</span>
                  <span className="ml-2 font-medium">{currentSchoolSession.startTime}</span>
                </div>
                <div>
                  <span className="text-blue-700">End Time:</span>
                  <span className="ml-2 font-medium">{currentSchoolSession.endTime}</span>
                </div>
              </div>
            </div>
          )}

          {/* Control Buttons */}
          <div className="flex flex-col sm:flex-row gap-2 pt-4 border-t">
            {!status.isInitialized ? (
              <Button
                onClick={initializeService}
                disabled={loading}
                className="w-full sm:w-auto flex items-center justify-center gap-2 text-sm"
              >
                <Play className="w-4 h-4 flex-shrink-0" />
                {t("admin.automaticQR.startService")}
              </Button>
            ) : (
              <>
                <Button
                  onClick={manualTrigger}
                  variant="outline"
                  className="w-full sm:w-auto flex items-center justify-center gap-2 text-sm"
                >
                  <RefreshCw className="w-4 h-4 flex-shrink-0" />
                  <span className="truncate">{t("admin.automaticQR.generateNow")}</span>
                </Button>

                <Button
                  onClick={updateStatus}
                  variant="outline"
                  className="w-full sm:w-auto flex items-center justify-center gap-2 text-sm"
                >
                  <Activity className="w-4 h-4 flex-shrink-0" />
                  <span className="truncate">{t("admin.automaticQR.refreshStatus")}</span>
                </Button>

                <Button
                  onClick={stopService}
                  variant="destructive"
                  className="w-full sm:w-auto flex items-center justify-center gap-2 text-sm"
                >
                  <Square className="w-4 h-4 flex-shrink-0" />
                  <span className="truncate">{t("admin.automaticQR.stopService")}</span>
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Session Details */}
      {sessions.length > 0 && (
        <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-green-600" />
              {t("admin.automaticQR.activeSessions")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {sessions.map((session, index) => (
                <div
                  key={session.schoolId}
                  className="flex flex-col sm:flex-row sm:items-center justify-between p-3 border rounded-lg bg-green-50 gap-2"
                >
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-green-900 text-sm sm:text-base">
                      {t("admin.automaticQR.school")} {session.schoolId.substring(0, 8)}...
                    </h4>
                    <p className="text-xs sm:text-sm text-green-700">
                      {t("admin.automaticQR.roomsCount", "{{count}} rooms", { count: session.roomCount })} • {session.startTime} - {session.endTime}
                    </p>
                  </div>
                  <Badge className="bg-green-100 text-green-800 self-start sm:self-center text-xs">
                    {t("admin.automaticQR.active")}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Information Card */}
      <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-lg">{t("admin.automaticQR.howItWorks")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-sm text-gray-600">
          {/* Configuration Info */}
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2 text-sm sm:text-base">{t("admin.automaticQR.currentConfiguration")}</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-blue-700">{t("admin.automaticQR.qrExpiryTime")}:</span>
                <span className="ml-2 font-medium">{Math.round(getQRExpirySeconds() / 60)} {t("admin.automaticQR.minutes")}</span>
              </div>
              <div>
                <span className="text-blue-700">{t("admin.automaticQR.generationInterval")}:</span>
                <span className="ml-2 font-medium">{Math.round(getQRExpirySeconds() * 0.75)} {t("admin.automaticQR.seconds")}</span>
              </div>
              <div>
                <span className="text-blue-700">{t("admin.automaticQR.transitionTiming")}:</span>
                <span className="ml-2 font-medium">{t("admin.automaticQR.oneSecondBeforeExpiry")}</span>
              </div>
              <div>
                <span className="text-blue-700">{t("admin.automaticQR.source")}:</span>
                <span className="ml-2 font-medium">{t("admin.automaticQR.envConfiguration")}</span>
              </div>
            </div>
          </div>

          {/* How it works */}
          <div className="space-y-3">
            <div className="flex items-start gap-2">
              <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
              <p className="text-xs sm:text-sm">
                <strong>{t("admin.automaticQR.automaticStart")}:</strong> {t("admin.automaticQR.automaticStartDescription")}
              </p>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
              <p className="text-xs sm:text-sm">
                <strong>{t("admin.automaticQR.smartTiming")}:</strong> {t("admin.automaticQR.smartTimingDescription", "New QR generated every {{interval}} seconds, transitions at 1 second remaining", { interval: Math.round(getQRExpirySeconds() * 0.75) })}
              </p>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
              <p className="text-xs sm:text-sm">
                <strong>{t("admin.automaticQR.configurableExpiry")}:</strong> {t("admin.automaticQR.configurableExpiryDescription", "QR codes expire after {{minutes}} minutes (set in .env file)", { minutes: Math.round(getQRExpirySeconds() / 60) })}
              </p>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
              <p className="text-xs sm:text-sm">
                <strong>{t("admin.automaticQR.allRooms")}:</strong> {t("admin.automaticQR.allRoomsDescription")}
              </p>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
              <p className="text-xs sm:text-sm">
                <strong>{t("admin.automaticQR.automaticStop")}:</strong> {t("admin.automaticQR.automaticStopDescription")}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
