import LoginForm from "@/components/auth/LoginForm";
import Navbar from "@/components/shared/Navbar";
import { useBranding } from "@/hooks/useBranding";
import { LoginLogo } from "@/components/ui/logo";

export default function Login() {
  const { uiText } = useBranding();

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="w-full max-w-md">
          <div className="text-center mb-6">
            <LoginLogo className="mb-4" />
            <h1 className="text-2xl font-bold text-primary">
              {uiText.LOGIN_TITLE}
            </h1>
          </div>
          <LoginForm />
        </div>
      </div>
    </div>
  );
}
