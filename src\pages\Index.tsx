import { useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import Navbar from "@/components/shared/Navbar";
import Footer from "@/components/shared/Footer";
import {
  ShieldCheck,
  Users,
  BarChart4,
  CheckCircle,
  Clock,
  ClipboardCheck,
} from "lucide-react";
import { useBranding } from "@/hooks/useBranding";
import { Logo } from "@/components/ui/logo";
import { useTranslation } from "react-i18next";

export default function Index() {
  const navigate = useNavigate();
  const { branding } = useBranding();
  const { t } = useTranslation();

  useEffect(() => {
    // Check if user is already logged in and redirect to their dashboard
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      const user = JSON.parse(storedUser);
      navigate(`/${user.role}`);
    }
  }, [navigate]);

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-primary text-white py-16 md:py-24 dark:bg-[#F39228]">
          <div className="container mx-auto px-4 text-center">
            <div className="mb-8">
              <Logo
                variant="square"
                size="xl"
                className="justify-center mb-6"
              />
            </div>
            <h1 className="text-3xl md:text-5xl font-bold mb-4">
              {branding.APP_NAME}
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              {branding.APP_DESCRIPTION}
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button
                size="lg"
                className="bg-white text-primary hover:bg-gray-100 dark:bg-white dark:text-[#F39228] dark:hover:bg-gray-100"
                asChild
              >
                <Link to="/login" className="dark:text-[#F39228]">{t('landing.getStarted')}</Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-gray-50 dark:bg-gray-800/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">
              {t('landing.keyFeatures')}
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center justify-center h-12 w-12 bg-primary/10 text-primary rounded-full mb-4 mx-auto">
                  <ClipboardCheck size={24} />
                </div>
                <h3 className="text-xl font-bold text-center mb-2">
                  {t('landing.features.smartTracking.title')}
                </h3>
                <p className="text-center text-gray-600">
                  {t('landing.features.smartTracking.description')}
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center justify-center h-12 w-12 bg-primary/10 text-primary rounded-full mb-4 mx-auto">
                  <ShieldCheck size={24} />
                </div>
                <h3 className="text-xl font-bold text-center mb-2">
                  {t('landing.features.antiFraud.title')}
                </h3>
                <p className="text-center text-gray-600">
                  {t('landing.features.antiFraud.description')}
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center justify-center h-12 w-12 bg-primary/10 text-primary rounded-full mb-4 mx-auto">
                  <BarChart4 size={24} />
                </div>
                <h3 className="text-xl font-bold text-center mb-2">
                  {t('landing.features.analytics.title')}
                </h3>
                <p className="text-center text-gray-600">
                  {t('landing.features.analytics.description')}
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* How it Works */}
        <section className="py-16 bg-white dark:bg-gray-900/20">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">
              {t('landing.howItWorks.title')}
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="flex flex-col items-center">
                <div className="flex items-center justify-center h-12 w-12 bg-secondary/10 text-secondary rounded-full mb-4">
                  <span className="font-bold">1</span>
                </div>
                <h3 className="text-xl font-bold text-center mb-2">
                  {t('landing.howItWorks.step1.title')}
                </h3>
                <p className="text-center text-gray-600">
                  {t('landing.howItWorks.step1.description')}
                </p>
              </div>

              <div className="flex flex-col items-center">
                <div className="flex items-center justify-center h-12 w-12 bg-secondary/10 text-secondary rounded-full mb-4">
                  <span className="font-bold">2</span>
                </div>
                <h3 className="text-xl font-bold text-center mb-2">
                  {t('landing.howItWorks.step2.title')}
                </h3>
                <p className="text-center text-gray-600">
                  {t('landing.howItWorks.step2.description')}
                </p>
              </div>

              <div className="flex flex-col items-center">
                <div className="flex items-center justify-center h-12 w-12 bg-secondary/10 text-secondary rounded-full mb-4">
                  <span className="font-bold">3</span>
                </div>
                <h3 className="text-xl font-bold text-center mb-2">
                  {t('landing.howItWorks.step3.title')}
                </h3>
                <p className="text-center text-gray-600">
                  {t('landing.howItWorks.step3.description')}
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* User Types */}
        <section className="py-16 bg-gray-50 dark:bg-gray-800/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">
              {t('landing.userTypes.title')}
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center justify-center h-12 w-12 bg-blue-100 text-blue-600 rounded-full mb-4 mx-auto">
                  <Users size={24} />
                </div>
                <h3 className="text-xl font-bold text-center mb-2">{t('landing.userTypes.students.title')}</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>{t('landing.userTypes.students.feature1')}</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>{t('landing.userTypes.students.feature2')}</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>{t('landing.userTypes.students.feature3')}</span>
                  </li>
                </ul>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center justify-center h-12 w-12 bg-purple-100 text-purple-600 rounded-full mb-4 mx-auto">
                  <BarChart4 size={24} />
                </div>
                <h3 className="text-xl font-bold text-center mb-2">{t('landing.userTypes.teachers.title')}</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>{t('landing.userTypes.teachers.feature1')}</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>{t('landing.userTypes.teachers.feature2')}</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>{t('landing.userTypes.teachers.feature3')}</span>
                  </li>
                </ul>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center justify-center h-12 w-12 bg-teal-100 text-teal-600 rounded-full mb-4 mx-auto">
                  <ShieldCheck size={24} />
                </div>
                <h3 className="text-xl font-bold text-center mb-2">
                  {t('landing.userTypes.administrators.title')}
                </h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>{t('landing.userTypes.administrators.feature1')}</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>{t('landing.userTypes.administrators.feature2')}</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>{t('landing.userTypes.administrators.feature3')}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary text-white dark:bg-[#F39228]">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-4">
              {t('landing.cta.title')}
            </h2>
            <p className="text-xl mb-8 max-w-2xl mx-auto">
              {t('landing.cta.description')}
            </p>
            <Button
              size="lg"
              className="bg-white text-primary hover:bg-gray-100 dark:bg-white dark:text-[#F39228] dark:hover:bg-gray-100"
              asChild
            >
              <Link to="/login" className="dark:text-[#F39228]">{t('landing.getStarted')}</Link>
            </Button>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
