// Test script for email and SMS notifications
import { createClient } from "@supabase/supabase-js";
import sgMail from "@sendgrid/mail";

// Supabase configuration
const supabaseUrl =
  process.env.SUPABASE_URL || "https://wclwxrilybnzkhvqzbmy.supabase.co";
const supabaseKey =
  process.env.SUPABASE_ANON_KEY ||
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndjbHd4cmlseWJuemtodnF6Ym15Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzA5NTksImV4cCI6MjA2MTI0Njk1OX0.MIARsz34RX0EftvwUkWIrEYQqE8VstxaCI31mjLhSHw";
const supabase = createClient(supabaseUrl, supabaseKey);

// Test email configuration
const testEmail = async () => {
  try {
    console.log("Testing email configuration...");

    // Get email configuration from system_settings
    const { data, error } = await supabase
      .from("system_settings")
      .select("setting_value")
      .eq("setting_name", "email_service_config")
      .single();

    if (error) {
      console.error("Error fetching email configuration:", error);
      return;
    }

    const config = data.setting_value;
    console.log("Email configuration:", config);

    if (!config.apiKey || !config.fromEmail) {
      console.error("Incomplete email configuration");
      return;
    }

    // Set SendGrid API key
    sgMail.setApiKey(config.apiKey);

    // Send test email
    const testEmailAddress = process.argv[2] || "<EMAIL>";
    const response = await sgMail.send({
      to: testEmailAddress,
      from: config.fromEmail,
      subject: "Attendance Tracking System - Test Email",
      text: "This is a test email from Attendance Tracking System. If you received this email, the email service is configured correctly.",
      html: "This is a test email from Attendance Tracking System. If you received this email, the email service is configured correctly.",
    });

    console.log("Email sent successfully:", response[0].statusCode);
  } catch (error) {
    console.error("Error testing email:", error);
    if (error.response) {
      console.error("SendGrid API error response:", error.response.body);
    }
  }
};

// Test SMS configuration
const testSMS = async () => {
  try {
    console.log("Testing SMS configuration...");

    // Get SMS configuration from system_settings
    const { data, error } = await supabase
      .from("system_settings")
      .select("setting_value")
      .eq("setting_name", "sms_service_config")
      .single();

    if (error) {
      console.error("Error fetching SMS configuration:", error);
      return;
    }

    const config = data.setting_value;
    console.log("SMS configuration:", config);

    if (!config.accountSid || !config.authToken || !config.phoneNumber) {
      console.error("Incomplete SMS configuration");
      return;
    }

    // Send test SMS using Twilio REST API
    const testPhoneNumber = process.argv[3] || "+**********";
    const twilioApiUrl = `https://api.twilio.com/2010-04-01/Accounts/${config.accountSid}/Messages.json`;

    // Create form data for the request
    const formData = new URLSearchParams();
    formData.append("To", testPhoneNumber);
    formData.append("From", config.phoneNumber);
    formData.append(
      "Body",
      "This is a test SMS from Attendance Tracking System. If you received this message, the SMS service is configured correctly."
    );

    // Create authorization header
    const auth = Buffer.from(
      `${config.accountSid}:${config.authToken}`
    ).toString("base64");

    // Send request to Twilio API
    const response = await fetch(twilioApiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${auth}`,
      },
      body: formData,
    });

    const data = await response.json();

    if (!response.ok) {
      console.error("Twilio API error:", data);
      return;
    }

    console.log("SMS sent successfully, SID:", data.sid);
  } catch (error) {
    console.error("Error testing SMS:", error);
  }
};

// Run tests
const runTests = async () => {
  await testEmail();
  console.log("-------------------");
  await testSMS();
};

runTests().catch(console.error);
