import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { format } from 'date-fns';
import { MapPin, ExternalLink, Trash2 } from 'lucide-react';

interface DistanceAlert {
  id: string;
  title: string;
  message: string;
  created_at: string;
  teacher_id: string;
  teacher_read_at: string | null;
  admin_read_at: string | null;
  metadata: {
    student_name: string;
    latitude: number;
    longitude: number;
    room_number: string;
    max_allowed_distance: number;
  };
}

export default function DistanceAlerts() {
  const [alerts, setAlerts] = useState<DistanceAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    fetchAlerts();
    const unsubscribe = subscribeToAlerts();
    return () => {
      unsubscribe();
    };
  }, []);

  const fetchAlerts = async () => {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('type', 'distance_alert')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setAlerts(data || []);
    } catch (error) {
      console.error('Error fetching alerts:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch distance alerts',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const subscribeToAlerts = () => {
    const subscription = supabase
      .channel('distance_alerts')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications',
          filter: 'type=eq.distance_alert'
        },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            setAlerts(prev => [payload.new as DistanceAlert, ...prev]);
            toast({
              title: 'New Distance Alert',
              description: payload.new.message,
            });
          } else if (payload.eventType === 'DELETE') {
            setAlerts(prev => prev.filter(alert => alert.id !== payload.old.id));
          } else if (payload.eventType === 'UPDATE') {
            setAlerts(prev => prev.map(alert => 
              alert.id === payload.new.id ? payload.new as DistanceAlert : alert
            ));
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  };

  const markAsRead = async (alertId: string) => {
    try {
      const updateData = user?.role === 'admin' 
        ? { admin_read_at: new Date().toISOString() }
        : { teacher_read_at: new Date().toISOString() };

      const { error } = await supabase
        .from('notifications')
        .update(updateData)
        .eq('id', alertId);

      if (error) throw error;

      setAlerts(prev =>
        prev.map(alert =>
          alert.id === alertId
            ? { ...alert, ...updateData }
            : alert
        )
      );
    } catch (error) {
      console.error('Error marking alert as read:', error);
      toast({
        title: 'Error',
        description: 'Failed to mark alert as read',
        variant: 'destructive',
      });
    }
  };

  const deleteAlert = async (alertId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', alertId);

      if (error) throw error;

      setAlerts(prev => prev.filter(alert => alert.id !== alertId));
      toast({
        title: 'Success',
        description: 'Alert deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting alert:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete alert',
        variant: 'destructive',
      });
    }
  };

  const openInMaps = (lat: number, lng: number) => {
    window.open(`https://www.google.com/maps?q=${lat},${lng}`, '_blank');
  };

  const isAlertRead = (alert: DistanceAlert) => {
    return user?.role === 'admin' 
      ? alert.admin_read_at !== null
      : alert.teacher_read_at !== null;
  };

  if (loading) {
    return <div>Loading alerts...</div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Distance Alerts</span>
          <Badge variant="secondary">
            {alerts.filter(a => !isAlertRead(a)).length} Unread
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {alerts.length === 0 ? (
            <p className="text-gray-500">No distance alerts to display</p>
          ) : (
            alerts.map(alert => (
              <Card key={alert.id} className={isAlertRead(alert) ? 'opacity-70' : ''}>
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-semibold">{alert.metadata.student_name}</h3>
                      <p className="text-sm text-gray-500">
                        Room {alert.metadata.room_number}
                      </p>
                      <p className="text-sm mt-2">{alert.message}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        {format(new Date(alert.created_at), 'PPp')}
                      </p>
                    </div>
                    <div className="flex flex-col gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openInMaps(alert.metadata.latitude, alert.metadata.longitude)}
                      >
                        <MapPin className="h-4 w-4 mr-1" />
                        View Location
                      </Button>
                      {!isAlertRead(alert) && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => markAsRead(alert.id)}
                        >
                          Mark as Read
                        </Button>
                      )}
                      {(user?.role === 'admin' || (user?.role === 'teacher' && user.id === alert.teacher_id)) && (
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => deleteAlert(alert.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Delete
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
} 