import { supabase } from "@/lib/supabase";

export interface QRCodePayload {
  room_id: string;
  session_id: string;
  timestamp: string;
  expires_at: string;
  school_id: string;
  block_id: string;
  nonce: string;
  challenge: string;
}

export interface SignedQRData extends QRCodePayload {
  signature: string;
}

export interface GenerateQRRequest {
  room_id: string;
  school_id: string;
  block_id: string;
  expiry_seconds?: number;
  user_id: string;
}

export interface ValidateQRRequest {
  qr_data: SignedQRData;
  student_id: string;
}

export interface QRValidationResult {
  isValid: boolean;
  error?: string;
  sessionId?: string;
}

class QRSecurityAPI {
  private baseUrl: string;

  constructor() {
    // Use Supabase edge function URL
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    this.baseUrl = `${supabaseUrl}/functions/v1/qr-security`;
  }

  /**
   * Generate a secure QR code on the server
   */
  async generateQRCode(request: GenerateQRRequest): Promise<SignedQRData> {
    try {
      console.log("Generating QR code on server:", request);

      // Get current session for authentication
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        throw new Error("No authentication session");
      }

      const response = await fetch(this.baseUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session.access_token}`,
          apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
        },
        body: JSON.stringify({
          action: "generate",
          ...request,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Server error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || "Failed to generate QR code");
      }

      console.log("QR code generated successfully on server");
      return result.data;
    } catch (error) {
      console.error("Error generating QR code:", error);
      throw error;
    }
  }

  /**
   * Validate a QR code on the server
   */
  async validateQRCode(
    request: ValidateQRRequest
  ): Promise<QRValidationResult> {
    try {
      console.log("Validating QR code on server:", {
        session_id: request.qr_data.session_id,
        room_id: request.qr_data.room_id,
        student_id: request.student_id,
      });

      // Get current session for authentication
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        throw new Error("No authentication session");
      }

      const response = await fetch(this.baseUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session.access_token}`,
          apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
        },
        body: JSON.stringify({
          action: "validate",
          ...request,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Server error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || "Failed to validate QR code");
      }

      console.log("QR code validation result:", result.data);
      return result.data;
    } catch (error) {
      console.error("Error validating QR code:", error);
      throw error;
    }
  }

  /**
   * Parse QR code data from scanned string
   */
  parseQRData(qrString: string): SignedQRData | null {
    try {
      const data = JSON.parse(qrString);

      // Validate required fields
      const requiredFields = [
        "room_id",
        "session_id",
        "timestamp",
        "expires_at",
        "school_id",
        "block_id",
        "nonce",
        "challenge",
        "signature",
      ];

      for (const field of requiredFields) {
        if (!data[field]) {
          console.error(`Missing required field: ${field}`);
          return null;
        }
      }

      return data as SignedQRData;
    } catch (error) {
      console.error("Error parsing QR data:", error);
      return null;
    }
  }

  /**
   * Check if QR code has basic validity (client-side pre-check)
   */
  isQRCodeBasicallyValid(qrData: SignedQRData): {
    isValid: boolean;
    error?: string;
  } {
    // Check expiry (client-side pre-check)
    const expiryTime = new Date(qrData.expires_at);
    if (expiryTime.getTime() <= Date.now()) {
      return {
        isValid: false,
        error: "QR code has expired",
      };
    }

    // Check timestamp is not too old
    const createdTime = new Date(qrData.timestamp);
    const maxAge = 10 * 60 * 1000; // 10 minutes max age
    if (Date.now() - createdTime.getTime() > maxAge) {
      return {
        isValid: false,
        error: "QR code is too old",
      };
    }

    // Check timestamp is not in the future
    if (createdTime.getTime() > Date.now() + 60000) {
      return {
        isValid: false,
        error: "QR code timestamp is in the future",
      };
    }

    return { isValid: true };
  }
}

// Export singleton instance
export const qrSecurityAPI = new QRSecurityAPI();

// Legacy compatibility - these functions now use server-side validation with fallback
export async function generateSecureQRCode(
  roomId: string,
  schoolId: string,
  blockId: string,
  expirySeconds?: number,
  userId?: string
): Promise<SignedQRData> {
  // Try server-side generation first
  if (userId) {
    try {
      console.log("Attempting server-side QR generation...");
      return await qrSecurityAPI.generateQRCode({
        room_id: roomId,
        school_id: schoolId,
        block_id: blockId,
        expiry_seconds: expirySeconds,
        user_id: userId,
      });
    } catch (error) {
      console.warn(
        "Server-side QR generation failed, falling back to client-side:",
        error
      );
      // Fall through to client-side generation
    }
  }

  // Fallback to client-side generation (legacy)
  console.log("Using client-side QR generation (fallback)");
  const { generateSecureQRCode: legacyGenerate } = await import(
    "@/lib/utils/qr-security"
  );

  const qrData = legacyGenerate(
    roomId,
    schoolId,
    blockId,
    expirySeconds || 400
  );

  // IMPORTANT: Update database and broadcast to tablets for fallback mode
  try {
    const expiryTime = new Date(qrData.expires_at);
    const qrDataString = JSON.stringify(qrData);

    console.log("Updating database for client-side generated QR code...");

    // Update room in database
    const { error } = await supabase
      .from("rooms")
      .update({
        current_qr_code: qrDataString,
        qr_expiry: expiryTime.toISOString(),
      })
      .eq("id", roomId);

    if (error) {
      console.error("Error updating room QR code in fallback mode:", error);
    } else {
      console.log("Successfully updated database in fallback mode");

      // Broadcast to tablets via WebSocket
      try {
        const { websocketService } = await import(
          "@/lib/services/websocket-service"
        );
        websocketService.broadcastQRUpdate({
          type: "qr_generated",
          data: {
            room_id: roomId,
            block_id: blockId,
            school_id: schoolId,
            session_id: qrData.session_id,
            expires_at: expiryTime.toISOString(),
          },
        });
        console.log("Broadcasted QR update to tablets");
      } catch (wsError) {
        console.warn("WebSocket broadcast failed:", wsError);
      }
    }
  } catch (dbError) {
    console.error("Database update failed in fallback mode:", dbError);
  }

  return qrData;
}

export async function verifyCurrentQRCode(
  qrData: SignedQRData,
  studentId?: string
): Promise<{ isValid: boolean; error?: string }> {
  // Try server-side validation first
  if (studentId) {
    try {
      console.log("Attempting server-side QR validation...");

      // First do basic client-side checks
      const basicCheck = qrSecurityAPI.isQRCodeBasicallyValid(qrData);
      if (!basicCheck.isValid) {
        return basicCheck;
      }

      // Then do full server-side validation
      const result = await qrSecurityAPI.validateQRCode({
        qr_data: qrData,
        student_id: studentId,
      });

      return {
        isValid: result.isValid,
        error: result.error,
      };
    } catch (error) {
      console.warn(
        "Server-side QR validation failed, falling back to client-side:",
        error
      );
      // Fall through to client-side validation
    }
  }

  // Fallback to client-side validation (legacy)
  console.log("Using client-side QR validation (fallback)");
  const { verifyCurrentQRCode: legacyVerify } = await import(
    "@/lib/utils/qr-security"
  );
  return legacyVerify(qrData);
}

// Export types for backward compatibility
export type { SignedQRData as SignedQRData };
export type { QRCodePayload };

// Configuration functions (now return server-managed values)
export function getQRExpirySeconds(): number {
  // Use the correct environment variable name from .env file
  // Try both VITE_ and NEXT_PUBLIC_ prefixes for compatibility
  const viteVar = import.meta.env.VITE_QR_EXPIRY_SECONDS;
  const nextVar = import.meta.env.NEXT_PUBLIC_QR_EXPIRY_SECONDS;

  return parseInt(viteVar || nextVar || "400");
}

export function getQRSecurityConfig() {
  return {
    qrExpirySeconds: getQRExpirySeconds(),
    challengeRotationInterval: 30, // Server-managed
    challengeGracePeriodSlots: 10, // Server-managed
    totalGracePeriodSeconds: 300, // Server-managed
    effectiveValidityWindow: 330, // Server-managed
  };
}

export function getQRSecurityInfo(): string {
  const config = getQRSecurityConfig();
  return `QR Challenge rotates every ${config.challengeRotationInterval}s, valid for ${config.effectiveValidityWindow}s total (Server-Secured)`;
}
