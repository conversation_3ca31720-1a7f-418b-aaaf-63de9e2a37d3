import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { ParentContact } from "@/lib/types/parent-contact";
import { notifyParents } from "@/lib/services/notification-service";
import { useTranslation } from "react-i18next";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { SendHorizonal, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface TestNotificationDialogProps {
  studentId: string;
  studentName: string;
  parentContacts: ParentContact[];
}

export default function TestNotificationDialog({
  studentId,
  studentName,
  parentContacts,
}: TestNotificationDialogProps) {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [sending, setSending] = useState(false);
  const [notificationType, setNotificationType] = useState<"test" | "excuse_new" | "excuse_approved" | "excuse_rejected">("test");
  const [customMessage, setCustomMessage] = useState("");
  const { toast } = useToast();

  const handleSendTest = async () => {
    if (!studentId) return;
    
    try {
      setSending(true);
      
      // Prepare notification data
      const getTemplateType = (type: string): 'new' | 'approved' | 'rejected' => {
        switch (type) {
          case "excuse_approved":
            return "approved";
          case "excuse_rejected":
            return "rejected";
          case "excuse_new":
          default:
            return "new";
        }
      };

      const notificationData = {
        subject: getSubjectForType(notificationType, studentName),
        message: customMessage || getMessageForType(notificationType, studentName),
        studentName,
        excuseId: "test-notification",
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
        reason: notificationType === "test" ? t("admin.parentNotifications.testNotification") : t("admin.parentNotifications.sampleExcuseReason"),
        templateType: getTemplateType(notificationType), // Add template type
      };
      
      // Send notification
      const result = await notifyParents(studentId, notificationData);
      
      if (result.success) {
        toast({
          title: t("admin.parentNotifications.testNotificationSent"),
          description: t("admin.parentNotifications.testNotificationSentDescription", "Successfully sent test notification to parent(s) of {{studentName}}", { studentName }),
        });
        setOpen(false);
      } else {
        toast({
          title: t("admin.parentNotifications.notificationFailed"),
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("Error sending test notification:", error);
      toast({
        title: t("admin.parentNotifications.error"),
        description: t("admin.parentNotifications.failedToSendTestNotification", "Failed to send test notification: {{error}}", { error: error.message }),
        variant: "destructive",
      });
    } finally {
      setSending(false);
    }
  };
  
  const getSubjectForType = (type: string, name: string): string => {
    switch (type) {
      case "test":
        return t("admin.parentNotifications.testNotificationSubject", "Test Notification for {{name}}'s Parent", { name });
      case "excuse_new":
        return t("admin.parentNotifications.newAbsenceRequestSubject", "New Absence Request from {{name}}", { name });
      case "excuse_approved":
        return t("admin.parentNotifications.absenceRequestApprovedSubject", "Absence Request Approved for {{name}}", { name });
      case "excuse_rejected":
        return t("admin.parentNotifications.absenceRequestRejectedSubject", "Absence Request Rejected for {{name}}", { name });
      default:
        return t("admin.parentNotifications.notificationSubject", "Notification for {{name}}'s Parent", { name });
    }
  };
  
  const getMessageForType = (type: string, name: string): string => {
    switch (type) {
      case "test":
        return t("admin.parentNotifications.testNotificationMessage", "This is a test notification for {{name}}'s parent. If you received this message, the notification system is working correctly.", { name });
      case "excuse_new":
        return t("admin.parentNotifications.newAbsenceRequestMessage", "Your child, {{name}}, has submitted a request for absence from school. This request is pending approval from school administration.", { name });
      case "excuse_approved":
        return t("admin.parentNotifications.absenceRequestApprovedMessage", "Your child's ({{name}}) absence request has been APPROVED by the school administration.", { name });
      case "excuse_rejected":
        return t("admin.parentNotifications.absenceRequestRejectedMessage", "Your child's ({{name}}) absence request has been REJECTED by the school administration. Please contact the school for more information.", { name });
      default:
        return t("admin.parentNotifications.notificationMessage", "This is a notification regarding your child, {{name}}.", { name });
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="ml-2">
          <SendHorizonal className="mr-2 h-4 w-4" />
          {t("admin.parentNotifications.testNotification")}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-base sm:text-lg">{t("admin.parentNotifications.sendTestNotification")}</DialogTitle>
          <DialogDescription className="text-sm">
            {t("admin.parentNotifications.sendTestNotificationDescription")}
          </DialogDescription>
        </DialogHeader>
        
        {parentContacts.length === 0 ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>{t("admin.parentNotifications.noParentContacts")}</AlertTitle>
            <AlertDescription>
              {t("admin.parentNotifications.noParentContactsDescription")}
            </AlertDescription>
          </Alert>
        ) : (
          <div className="space-y-3 sm:space-y-4 py-3 sm:py-4">
            <div className="space-y-2">
              <Label htmlFor="notification-type" className="text-sm font-medium">{t("admin.parentNotifications.notificationType")}</Label>
              <Select
                value={notificationType}
                onValueChange={(value: any) => setNotificationType(value)}
              >
                <SelectTrigger id="notification-type" className="text-sm">
                  <SelectValue placeholder={t("admin.parentNotifications.selectNotificationType")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="test">{t("admin.parentNotifications.testMessage")}</SelectItem>
                  <SelectItem value="excuse_new">{t("admin.parentNotifications.newExcuseRequest")}</SelectItem>
                  <SelectItem value="excuse_approved">{t("admin.parentNotifications.excuseApproved")}</SelectItem>
                  <SelectItem value="excuse_rejected">{t("admin.parentNotifications.excuseRejected")}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="custom-message" className="text-sm font-medium">{t("admin.parentNotifications.customMessageOptional")}</Label>
              <Textarea
                id="custom-message"
                placeholder={t("admin.parentNotifications.customMessagePlaceholder")}
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
                className="min-h-[80px] sm:min-h-[100px] text-sm"
              />
              <p className="text-xs text-muted-foreground">
                {t("admin.parentNotifications.leaveBlankForDefault")}
              </p>
            </div>
            
            <Alert>
              <AlertCircle className="h-4 w-4 flex-shrink-0" />
              <AlertTitle className="text-sm">{t("admin.parentNotifications.notificationRecipients")}</AlertTitle>
              <AlertDescription>
                <p className="mb-2 text-sm">{t("admin.parentNotifications.testWillBeSentTo")}</p>
                <ul className="list-disc pl-5 space-y-1 text-xs sm:text-sm">
                  {parentContacts.map((contact) => (
                    <li key={contact.id} className="break-words">
                      <span className="font-medium">{contact.parent_name}</span> via {contact.notification_method === "email"
                        ? t("admin.parentNotifications.viaEmail", "Email ({{email}})", { email: contact.email })
                        : contact.notification_method === "sms"
                          ? t("admin.parentNotifications.viaSMS", "SMS ({{phone}})", { phone: contact.phone })
                          : contact.notification_method === "both"
                            ? t("admin.parentNotifications.viaEmailAndSMS", "Email & SMS ({{email}}, {{phone}})", { email: contact.email, phone: contact.phone })
                            : t("admin.parentNotifications.noNotifications")}
                    </li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          </div>
        )}

        <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
          <Button variant="outline" onClick={() => setOpen(false)} className="w-full sm:w-auto text-sm">
            {t("admin.parentNotifications.cancel")}
          </Button>
          <Button
            onClick={handleSendTest}
            disabled={sending || parentContacts.length === 0}
            className="w-full sm:w-auto text-sm"
          >
            {sending ? t("admin.parentNotifications.sending") : t("admin.parentNotifications.sendTestNotification")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
