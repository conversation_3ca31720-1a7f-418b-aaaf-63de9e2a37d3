-- =====================================================
-- REMINDER SYSTEM FIXES - Run this if you have issues
-- =====================================================

-- Fix 1: Handle existing reminder_settings records
-- ================================================

-- If you already have reminder_settings table with data, this will clean it up
-- and recreate with proper constraints

-- Drop existing unique constraint if it exists
ALTER TABLE reminder_settings DROP CONSTRAINT IF EXISTS reminder_settings_school_id_key;

-- Add proper unique constraint
ALTER TABLE reminder_settings ADD CONSTRAINT reminder_settings_school_id_unique UNIQUE (school_id);

-- Fix 2: Handle existing reminder_sent_records
-- ===========================================

-- Drop existing unique constraint if it exists
ALTER TABLE reminder_sent_records DROP CONSTRAINT IF EXISTS reminder_sent_records_school_id_date_key;

-- Add proper unique constraint for school_id and date combination
ALTER TABLE reminder_sent_records ADD CONSTRAINT reminder_sent_records_school_id_date_unique UNIQUE (school_id, date);

-- Fix 3: Clean up any duplicate records (if they exist)
-- ====================================================

-- Remove duplicate reminder_settings (keep the most recent)
WITH ranked_settings AS (
    SELECT id, 
           ROW_NUMBER() OVER (PARTITION BY school_id ORDER BY updated_at DESC, created_at DESC) as rn
    FROM reminder_settings
)
DELETE FROM reminder_settings 
WHERE id IN (
    SELECT id FROM ranked_settings WHERE rn > 1
);

-- Remove duplicate reminder_sent_records (keep the most recent)
WITH ranked_records AS (
    SELECT id, 
           ROW_NUMBER() OVER (PARTITION BY school_id, date ORDER BY sent_at DESC, created_at DESC) as rn
    FROM reminder_sent_records
)
DELETE FROM reminder_sent_records 
WHERE id IN (
    SELECT id FROM ranked_records WHERE rn > 1
);

-- Fix 4: Ensure proper foreign key relationships
-- =============================================

-- Check if profiles table has proper relationship with rooms
-- This query will show you the relationships
SELECT 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name='profiles'
AND kcu.column_name = 'room_id';

-- If the above query shows multiple relationships, you might need to clean them up
-- This is usually not an issue, but if you have problems, you can:

-- Option 1: Drop and recreate the foreign key (only if needed)
-- ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_room_id_fkey;
-- ALTER TABLE profiles ADD CONSTRAINT profiles_room_id_fkey FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE SET NULL;

-- Fix 5: Verify everything is working
-- ==================================

-- Check reminder_settings table
SELECT 'reminder_settings table check' as test, COUNT(*) as count FROM reminder_settings;

-- Check reminder_sent_records table  
SELECT 'reminder_sent_records table check' as test, COUNT(*) as count FROM reminder_sent_records;

-- Check constraints
SELECT 
    constraint_name, 
    table_name, 
    constraint_type 
FROM information_schema.table_constraints 
WHERE table_name IN ('reminder_settings', 'reminder_sent_records')
AND constraint_type = 'UNIQUE';

-- Check triggers
SELECT 
    trigger_name, 
    event_manipulation, 
    event_object_table 
FROM information_schema.triggers 
WHERE trigger_name IN ('update_reminder_settings_updated_at', 'trigger_mark_late_after_reminder');

-- =====================================================
-- FIXES COMPLETE
-- =====================================================

-- Fix 6: Update Late Marking Logic (IMPORTANT FIX)
-- =================================================

-- The original logic was marking students as late even if they never scanned
-- This fixes it to only mark students as late when they actually scan AFTER the reminder

CREATE OR REPLACE FUNCTION mark_late_after_reminder()
RETURNS TRIGGER AS $$
BEGIN
    -- Only process if this is a student checking in (not manual teacher entry)
    -- and the status would normally be 'present'
    IF NEW.status = 'present' AND (NEW.verification_method IS NULL OR NEW.verification_method != 'manual') THEN
        -- Check if there was an automated reminder sent today for this school
        -- AND the student is checking in AFTER the reminder was sent
        IF EXISTS (
            SELECT 1 FROM reminder_sent_records
            WHERE school_id = NEW.school_id
            AND date = CURRENT_DATE
            AND sent_at < NEW.timestamp
        ) THEN
            -- Student scanned AFTER automated reminder was sent, mark as late
            NEW.status = 'late';

            -- Log this for debugging (optional)
            -- RAISE NOTICE 'Student % marked as late - checked in after automated reminder', NEW.student_id;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger with the fixed function
DROP TRIGGER IF EXISTS trigger_mark_late_after_reminder ON attendance_records;
CREATE TRIGGER trigger_mark_late_after_reminder
    BEFORE INSERT ON attendance_records
    FOR EACH ROW
    EXECUTE FUNCTION mark_late_after_reminder();

-- If you still have issues, you can completely reset the reminder tables:
-- (UNCOMMENT ONLY IF NEEDED - THIS WILL DELETE ALL REMINDER DATA)

-- DROP TABLE IF EXISTS reminder_sent_records CASCADE;
-- DROP TABLE IF EXISTS reminder_settings CASCADE;
--
-- Then re-run the original migration script
