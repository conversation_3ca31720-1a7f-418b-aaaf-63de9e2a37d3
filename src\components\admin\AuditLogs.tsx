import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardT<PERSON>le,
  CardFooter,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import { useTranslation } from "react-i18next";
import {
  AuditActionType,
  AuditEntityType,
  getSchoolAuditLogs,
  getSystemAuditLogs,
} from "@/lib/utils/audit-logger";
import {
  Search,
  RefreshCw,
  AlertTriangle,
  Shield,
  User,
  School,
  FileText,
  Clock,
  ChevronLeft,
  ChevronRight,
  Loader2,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface AuditLog {
  id: string;
  school_id: string;
  user_id: string;
  action_type: AuditActionType;
  entity_type: AuditEntityType;
  entity_id: string;
  details: Record<string, any>;
  ip_address: string;
  user_agent: string;
  created_at: string;
  users?: {
    email: string;
  };
  schools?: {
    name: string;
  };
}

export default function AuditLogs() {
  const { toast } = useToast();
  const { profile } = useAuth();
  const { currentSchool, isSystemAdmin } = useSchool();
  const { t } = useTranslation();
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [actionFilter, setActionFilter] = useState<string>("all");
  const [entityFilter, setEntityFilter] = useState<string>("all");
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);

  const pageSize = 20;

  useEffect(() => {
    fetchLogs();
  }, [currentSchool, page, actionFilter, entityFilter]);

  const fetchLogs = async () => {
    if (!profile) return;

    setLoading(true);
    try {
      let fetchedLogs: AuditLog[];

      if (isSystemAdmin && !currentSchool) {
        // System admin viewing all logs
        fetchedLogs = await getSystemAuditLogs(pageSize, page * pageSize);
      } else if (currentSchool) {
        // School admin or system admin viewing school logs
        fetchedLogs = await getSchoolAuditLogs(
          currentSchool.id,
          pageSize,
          page * pageSize
        );
      } else {
        fetchedLogs = [];
      }

      // Apply filters
      let filteredLogs = fetchedLogs;

      if (actionFilter !== "all") {
        filteredLogs = filteredLogs.filter(
          (log) => log.action_type === actionFilter
        );
      }

      if (entityFilter !== "all") {
        filteredLogs = filteredLogs.filter(
          (log) => log.entity_type === entityFilter
        );
      }

      setLogs(filteredLogs);

      // Estimate total pages (this is a simplification)
      setTotalPages(Math.max(1, Math.ceil(fetchedLogs.length / pageSize)));
    } catch (error) {
      console.error("Error fetching audit logs:", error);
      toast({
        title: "Error",
        description: "Failed to fetch audit logs",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    // Reset page when searching
    setPage(0);
    fetchLogs();
  };

  const handleRefresh = () => {
    fetchLogs();
  };

  const handleViewDetails = (log: AuditLog) => {
    setSelectedLog(log);
    setIsDetailsOpen(true);
  };

  const getActionTypeLabel = (actionType: AuditActionType | string): string => {
    // Convert the action type to lowercase for consistent translation key lookup
    const normalizedActionType = actionType.toLowerCase();

    return t(
      `admin.auditLogs.actions.${normalizedActionType}`,
      actionType
        .split("_")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(" ")
    );
  };

  const getEntityTypeLabel = (entityType: AuditEntityType | string): string => {
    // Convert the entity type to lowercase and replace any uppercase letters with underscores
    const normalizedEntityType = entityType.toLowerCase().replace(/([A-Z])/g, '_$1');

    return t(
      `admin.auditLogs.entities.${normalizedEntityType}`,
      entityType
        .split("_")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(" ")
    );
  };

  const getActionIcon = (actionType: AuditActionType) => {
    if (actionType.includes("SECURITY") || actionType.includes("ALERT")) {
      return <AlertTriangle className="h-4 w-4 text-amber-500" />;
    } else if (
      actionType.includes("USER") ||
      actionType.includes("LOGIN") ||
      actionType.includes("LOGOUT")
    ) {
      return <User className="h-4 w-4 text-blue-500" />;
    } else if (actionType.includes("SCHOOL")) {
      return <School className="h-4 w-4 text-green-500" />;
    } else if (actionType.includes("PERMISSION")) {
      return <Shield className="h-4 w-4 text-purple-500" />;
    } else if (actionType.includes("DATA")) {
      return <FileText className="h-4 w-4 text-gray-500" />;
    } else {
      return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + " " + date.toLocaleTimeString();
  };

  // Filter logs by search query
  const filteredLogs = logs.filter((log) => {
    const searchLower = searchQuery.toLowerCase();
    return (
      (log.users?.email &&
        log.users.email.toLowerCase().includes(searchLower)) ||
      (log.schools?.name &&
        log.schools.name.toLowerCase().includes(searchLower)) ||
      log.action_type.toLowerCase().includes(searchLower) ||
      log.entity_type.toLowerCase().includes(searchLower) ||
      (log.ip_address && log.ip_address.includes(searchQuery))
    );
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">
          {t("admin.auditLogs.title")}
        </CardTitle>
        <CardDescription>
          {isSystemAdmin && !currentSchool
            ? t("admin.auditLogs.description")
            : `${t("admin.auditLogs.description")} ${
                currentSchool?.name || "UMH"
              }`}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 flex gap-2">
              <Input
                placeholder={t("admin.auditLogs.searchPlaceholder")}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 h-9 text-sm"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={handleSearch}
                className="h-9"
              >
                <Search className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex flex-wrap gap-2">
              <Select value={actionFilter} onValueChange={setActionFilter}>
                <SelectTrigger className="w-[140px] sm:w-[180px] h-9 text-xs sm:text-sm">
                  <SelectValue
                    placeholder={t("admin.auditLogs.filterByAction")}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    {t("admin.auditLogs.allActions")}
                  </SelectItem>
                  {Object.values(AuditActionType).map((action) => (
                    <SelectItem key={action} value={action}>
                      {t(
                        `admin.auditLogs.actions.${action}`,
                        getActionTypeLabel(action)
                      )}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={entityFilter} onValueChange={setEntityFilter}>
                <SelectTrigger className="w-[140px] sm:w-[180px] h-9 text-xs sm:text-sm">
                  <SelectValue
                    placeholder={t("admin.auditLogs.filterByEntity")}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    {t("admin.auditLogs.allEntities")}
                  </SelectItem>
                  {Object.values(AuditEntityType).map((entity) => (
                    <SelectItem key={entity} value={entity}>
                      {t(
                        `admin.auditLogs.entities.${entity}`,
                        getEntityTypeLabel(entity)
                      )}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={loading}
                className="h-9"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : filteredLogs.length === 0 ? (
            <div className="text-center py-8 border rounded-md bg-muted/10">
              <FileText className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-xl font-medium mb-2">
                {t("admin.auditLogs.noAuditLogs")}
              </h3>
              <p className="text-muted-foreground text-sm max-w-md mx-auto">
                {searchQuery || actionFilter !== "all" || entityFilter !== "all"
                  ? t("admin.auditLogs.noLogsMatchCriteria")
                  : t("admin.auditLogs.noAuditLogsRecorded")}
              </p>
            </div>
          ) : (
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-xs sm:text-sm">
                      {t("admin.auditLogs.dateTime")}
                    </TableHead>
                    <TableHead className="text-xs sm:text-sm">
                      {t("admin.auditLogs.action")}
                    </TableHead>
                    <TableHead className="text-xs sm:text-sm">
                      {t("admin.auditLogs.entity")}
                    </TableHead>
                    <TableHead className="text-xs sm:text-sm">
                      {t("admin.auditLogs.user")}
                    </TableHead>
                    {isSystemAdmin && !currentSchool && (
                      <TableHead className="text-xs sm:text-sm">
                        {t("admin.auditLogs.school")}
                      </TableHead>
                    )}
                    <TableHead className="text-xs sm:text-sm">
                      {t("admin.auditLogs.ipAddress")}
                    </TableHead>
                    <TableHead className="text-xs sm:text-sm text-right">
                      {t("admin.auditLogs.details")}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="whitespace-nowrap text-xs sm:text-sm py-2 sm:py-4">
                        {formatDate(log.created_at)}
                      </TableCell>
                      <TableCell className="text-xs sm:text-sm py-2 sm:py-4">
                        <div className="flex items-center gap-1 sm:gap-2">
                          {getActionIcon(log.action_type)}
                          <span>{getActionTypeLabel(log.action_type)}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-xs sm:text-sm py-2 sm:py-4">
                        <Badge variant="outline" className="text-xs">
                          {getEntityTypeLabel(log.entity_type)}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-xs sm:text-sm py-2 sm:py-4">
                        {log.users?.email || "System"}
                      </TableCell>
                      {isSystemAdmin && !currentSchool && (
                        <TableCell className="text-xs sm:text-sm py-2 sm:py-4">
                          {log.schools?.name || "System"}
                        </TableCell>
                      )}
                      <TableCell className="text-xs sm:text-sm py-2 sm:py-4">
                        {log.ip_address || "N/A"}
                      </TableCell>
                      <TableCell className="text-right text-xs sm:text-sm py-2 sm:py-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewDetails(log)}
                          className="h-7 px-2 text-xs"
                        >
                          {t("admin.auditLogs.view")}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          <div className="flex flex-col xs:flex-row items-center justify-between gap-2">
            <p className="text-xs sm:text-sm text-muted-foreground order-2 xs:order-1">
              {t("admin.auditLogs.showing", {
                shown: filteredLogs.length,
                total: logs.length,
              })}
            </p>
            <div className="flex items-center space-x-2 order-1 xs:order-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(Math.max(0, page - 1))}
                disabled={page === 0 || loading}
                className="h-8 w-8 p-0"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <p className="text-xs sm:text-sm min-w-[80px] text-center">
                {t("admin.auditLogs.pagination", {
                  current: page + 1,
                  total: totalPages,
                })}
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(Math.min(totalPages - 1, page + 1))}
                disabled={page >= totalPages - 1 || loading}
                className="h-8 w-8 p-0"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>

      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>{t("admin.auditLogs.auditLogDetails")}</DialogTitle>
            <DialogDescription>
              {t("admin.auditLogs.detailedInformation")}
            </DialogDescription>
          </DialogHeader>

          {selectedLog && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium">
                    {t("admin.auditLogs.dateTime")}
                  </h4>
                  <p>{formatDate(selectedLog.created_at)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">
                    {t("admin.auditLogs.action")}
                  </h4>
                  <p>{getActionTypeLabel(selectedLog.action_type)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">
                    {t("admin.auditLogs.entityType")}
                  </h4>
                  <p>{getEntityTypeLabel(selectedLog.entity_type)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">
                    {t("admin.auditLogs.entityId")}
                  </h4>
                  <p className="font-mono text-xs">
                    {selectedLog.entity_id || t("admin.auditLogs.notAvailable")}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">
                    {t("admin.auditLogs.user")}
                  </h4>
                  <p>
                    {selectedLog.users?.email || t("admin.auditLogs.system")}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">
                    {t("admin.auditLogs.ipAddress")}
                  </h4>
                  <p>
                    {selectedLog.ip_address ||
                      t("admin.auditLogs.notAvailable")}
                  </p>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium">
                  {t("admin.auditLogs.userAgent")}
                </h4>
                <p className="text-xs font-mono break-all">
                  {selectedLog.user_agent || t("admin.auditLogs.notAvailable")}
                </p>
              </div>

              <div>
                <h4 className="text-sm font-medium">
                  {t("admin.auditLogs.details")}
                </h4>
                <pre className="bg-muted p-4 rounded-md text-xs font-mono overflow-auto max-h-60">
                  {JSON.stringify(selectedLog.details, null, 2) ||
                    t("admin.auditLogs.noDetailsAvailable")}
                </pre>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
}
