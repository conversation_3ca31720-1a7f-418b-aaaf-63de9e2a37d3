import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useTranslation } from "react-i18next";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { User } from "@/lib/types";

interface UserBlockDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUserUpdated: () => void;
}

export default function UserBlockDialog({
  user,
  open,
  onOpenChange,
  onUserUpdated,
}: UserBlockDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();

  const isBlocked = user?.is_blocked === true;
  const actionText = isBlocked ? "unblock" : "block";
  const actionTitle = isBlocked
    ? t("admin.userManagement.unblockUser")
    : t("admin.userManagement.blockUser");

  const handleToggleBlock = async () => {
    if (!user) return;

    setLoading(true);
    try {
      console.log(`Toggling block status for user: ${user.name} (${user.id})`);

      // Step 1: Update the auth.users table to actually block the user
      console.log(
        `Calling disable_auth_user for user ${
          user.id
        } with should_disable=${!isBlocked}`
      );
      const { data: authResult, error: authError } = await supabase.rpc(
        "disable_auth_user",
        {
          profile_id: user.id,
          should_disable: !isBlocked,
        }
      );

      if (authError) {
        console.error("Error disabling auth user:", authError);
        // Continue anyway to update the profiles table
      } else {
        console.log("Auth user disabled successfully:", authResult);
      }

      // Step 2: Update the profiles table for UI display
      const { error } = await supabase
        .from("profiles")
        .update({
          is_blocked: !isBlocked,
          blocked_at: !isBlocked ? new Date().toISOString() : null,
        })
        .eq("id", user.id);

      if (error) {
        console.error("Error updating block status in profiles:", error);

        // Try using our function as a fallback
        console.log("Trying with toggle_block_status function");
        const { data: funcResult, error: funcError } = await supabase.rpc(
          "toggle_block_status",
          {
            p_id: user.id,
            p_block: !isBlocked,
          }
        );

        if (funcError) {
          console.error("Function error:", funcError);
          throw new Error(`Failed to ${actionText} user: ${funcError.message}`);
        }

        console.log("Function result:", funcResult);
      }

      console.log(`User ${isBlocked ? "unblocked" : "blocked"} successfully`);

      // Close the dialog
      onOpenChange(false);

      // Show success message
      toast({
        title: "Success",
        description: `${user.name} has been ${
          isBlocked ? "unblocked" : "blocked"
        }.`,
        duration: 3000,
      });

      // Refresh the user list
      onUserUpdated();
    } catch (error: any) {
      console.error("Error toggling block status:", error);
      toast({
        title: "Error",
        description: error.message || `Failed to ${actionText} user`,
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{actionTitle}</AlertDialogTitle>
          <AlertDialogDescription>
            {isBlocked ? (
              <>
                Are you sure you want to unblock {user?.name}? This will allow
                the user to access the system again.
              </>
            ) : (
              <>
                Are you sure you want to block {user?.name}? This will prevent
                the user from accessing the system until they are unblocked.
              </>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleToggleBlock}
            className={isBlocked ? "bg-primary" : "bg-destructive"}
            disabled={loading}
          >
            {loading ? "Processing..." : actionTitle}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
