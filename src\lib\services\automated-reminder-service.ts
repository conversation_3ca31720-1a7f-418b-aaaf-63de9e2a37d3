import { supabase } from "@/lib/supabase";
import { createBulkLocalizedNotifications } from "@/lib/utils/notification-localization";

export interface ReminderSettings {
  id?: string;
  school_id: string;
  enabled: boolean;
  minutes_before_end: number;
  created_at?: string;
  updated_at?: string;
}

export interface ReminderSchedule {
  school_id: string;
  reminder_time: string;
  attendance_end_time: string;
  minutes_before: number;
}

/**
 * Automated Attendance Reminder Service
 * Sends reminders to absent students X minutes before attendance session ends
 * Marks students who check in after reminder as "late"
 */
export class AutomatedReminderService {
  private static instance: AutomatedReminderService;
  private reminderInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  private reminderSchedules: Map<string, ReminderSchedule> = new Map();
  private sentReminders: Set<string> = new Set(); // Track sent reminders by date-school

  private constructor() {}

  public static getInstance(): AutomatedReminderService {
    if (!AutomatedReminderService.instance) {
      AutomatedReminderService.instance = new AutomatedReminderService();
    }
    return AutomatedReminderService.instance;
  }

  /**
   * Start the automated reminder service
   * Checks every minute for reminders to send
   */
  public start(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    console.log("🔔 Automated Reminder Service started");

    // Check immediately
    this.checkAndSendReminders();

    // Check every minute
    this.reminderInterval = setInterval(() => {
      this.checkAndSendReminders();
    }, 60000); // 1 minute
  }

  /**
   * Stop the automated reminder service
   */
  public stop(): void {
    if (this.reminderInterval) {
      clearInterval(this.reminderInterval);
      this.reminderInterval = null;
    }
    this.isRunning = false;
    console.log("🔔 Automated Reminder Service stopped");
  }

  /**
   * Get reminder settings for a school
   */
  public async getReminderSettings(schoolId: string): Promise<ReminderSettings | null> {
    try {
      const { data, error } = await supabase
        .from("reminder_settings")
        .select("*")
        .eq("school_id", schoolId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      return data;
    } catch (error) {
      console.error("Error fetching reminder settings:", error);
      return null;
    }
  }

  /**
   * Update reminder settings for a school
   */
  public async updateReminderSettings(settings: ReminderSettings): Promise<ReminderSettings> {
    try {
      // First try to get existing settings
      const existingSettings = await this.getReminderSettings(settings.school_id);

      let data;
      if (existingSettings) {
        // Update existing record
        const { data: updateData, error: updateError } = await supabase
          .from("reminder_settings")
          .update({
            enabled: settings.enabled,
            minutes_before_end: settings.minutes_before_end,
            updated_at: new Date().toISOString(),
          })
          .eq("school_id", settings.school_id)
          .select()
          .single();

        if (updateError) throw updateError;
        data = updateData;
      } else {
        // Insert new record
        const { data: insertData, error: insertError } = await supabase
          .from("reminder_settings")
          .insert({
            school_id: settings.school_id,
            enabled: settings.enabled,
            minutes_before_end: settings.minutes_before_end,
          })
          .select()
          .single();

        if (insertError) throw insertError;
        data = insertData;
      }

      // Update local schedule
      await this.updateReminderSchedule(settings.school_id);

      return data;
    } catch (error) {
      console.error("Error updating reminder settings:", error);
      throw error;
    }
  }

  /**
   * Send manual reminder to absent students (like teacher functionality)
   */
  public async sendManualReminder(schoolId: string, teacherId?: string): Promise<{
    success: boolean;
    sentCount: number;
    error?: string;
  }> {
    try {
      const absentStudents = await this.getAbsentStudents(schoolId);
      
      if (absentStudents.length === 0) {
        return {
          success: true,
          sentCount: 0,
        };
      }

      const currentTime = new Date();
      const timeString = currentTime.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "numeric",
        hour12: true,
      });

      // Send notifications
      const notificationResult = await createBulkLocalizedNotifications({
        students: absentStudents,
        teacherId,
        type: "attendance",
        templateKey: "attendanceReminder",
        getTemplateParams: (student) => {
          const studentRoom = student.room_name || "your assigned room";
          return ["School Admin", studentRoom];
        },
        getMetadata: (student) => ({
          room_id: student.room_id || "",
          notification_type: "manual_attendance_reminder",
          reminder_time: timeString,
          target_type: "school",
          sender: "admin",
          school_id: schoolId,
        }),
        getRoomNumber: (student) => student.room_name || "",
      });

      return {
        success: notificationResult.successful > 0,
        sentCount: notificationResult.successful,
      };
    } catch (error) {
      console.error("Error sending manual reminder:", error);
      return {
        success: false,
        sentCount: 0,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Check if it's time to send reminders and send them
   */
  private async checkAndSendReminders(): Promise<void> {
    try {
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      const today = now.toISOString().split('T')[0];

      // Get all enabled reminder settings
      const { data: reminderSettings, error } = await supabase
        .from("reminder_settings")
        .select("*")
        .eq("enabled", true);

      if (error) {
        console.error("Error fetching reminder settings:", error);
        return;
      }

      if (!reminderSettings || reminderSettings.length === 0) {
        return;
      }

      // Get attendance settings to calculate reminder times
      const { data: attendanceSettingsArray, error: attendanceError } = await supabase
        .from("attendance_settings")
        .select("*")
        .limit(1);

      if (attendanceError || !attendanceSettingsArray || attendanceSettingsArray.length === 0) {
        console.error("Error fetching attendance settings:", attendanceError);
        return;
      }

      const attendanceSettings = attendanceSettingsArray[0];

      // Process each school's reminder settings
      for (const setting of reminderSettings) {
        const reminderKey = `${today}-${setting.school_id}`;
        
        // Skip if already sent today
        if (this.sentReminders.has(reminderKey)) {
          continue;
        }

        // Calculate reminder time
        const reminderTime = this.calculateReminderTime(
          attendanceSettings.recording_end_time,
          setting.minutes_before_end
        );

        // Check if it's time to send reminder
        if (currentTime === reminderTime) {
          console.log(`🔔 Sending automated reminder for school ${setting.school_id}`);
          
          const result = await this.sendAutomatedReminder(setting.school_id);
          
          if (result.success) {
            // Mark reminder as sent
            this.sentReminders.add(reminderKey);
            
            // Store reminder sent record for late marking
            await this.storeReminderSentRecord(setting.school_id, now);
            
            console.log(`✅ Automated reminder sent to ${result.sentCount} students`);
          }
        }
      }

      // Clean up old sent reminders (keep only today's)
      this.cleanupSentReminders(today);
    } catch (error) {
      console.error("Error in checkAndSendReminders:", error);
    }
  }

  /**
   * Calculate the time to send reminder based on end time and minutes before
   */
  private calculateReminderTime(endTime: string, minutesBefore: number): string {
    const [hours, minutes] = endTime.split(':').map(Number);
    const endDate = new Date();
    endDate.setHours(hours, minutes, 0, 0);
    
    const reminderDate = new Date(endDate.getTime() - (minutesBefore * 60 * 1000));
    
    return `${reminderDate.getHours().toString().padStart(2, '0')}:${reminderDate.getMinutes().toString().padStart(2, '0')}`;
  }

  /**
   * Send automated reminder to absent students
   */
  private async sendAutomatedReminder(schoolId: string): Promise<{
    success: boolean;
    sentCount: number;
  }> {
    try {
      const absentStudents = await this.getAbsentStudents(schoolId);
      
      if (absentStudents.length === 0) {
        return { success: true, sentCount: 0 };
      }

      const currentTime = new Date();
      const timeString = currentTime.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "numeric",
        hour12: true,
      });

      // Send notifications with automated reminder template
      const notificationResult = await createBulkLocalizedNotifications({
        students: absentStudents,
        type: "attendance",
        templateKey: "automatedAttendanceReminder",
        getTemplateParams: (student) => {
          const studentRoom = student.room_name || "your assigned room";
          return [studentRoom, timeString];
        },
        getMetadata: (student) => ({
          room_id: student.room_id || "",
          notification_type: "automated_attendance_reminder",
          reminder_time: timeString,
          target_type: "school",
          sender: "system",
          school_id: schoolId,
        }),
        getRoomNumber: (student) => student.room_name || "",
      });

      return {
        success: notificationResult.successful > 0,
        sentCount: notificationResult.successful,
      };
    } catch (error) {
      console.error("Error sending automated reminder:", error);
      return { success: false, sentCount: 0 };
    }
  }

  /**
   * Get absent students for a school
   */
  private async getAbsentStudents(schoolId: string): Promise<any[]> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Get all students in the school
      const { data: allStudents, error: studentsError } = await supabase
        .from("profiles")
        .select(`
          id,
          name,
          email,
          room_id
        `)
        .eq("school_id", schoolId)
        .eq("role", "student");

      if (studentsError) throw studentsError;

      if (!allStudents || allStudents.length === 0) {
        return [];
      }

      // Get room data for students who have room_id
      const roomIds = [...new Set(allStudents.map(student => student.room_id).filter(Boolean))];
      let roomsData: any[] = [];

      if (roomIds.length > 0) {
        const { data: rooms, error: roomsError } = await supabase
          .from("rooms")
          .select("id, name")
          .in("id", roomIds);

        if (roomsError) {
          console.error("Error fetching rooms:", roomsError);
        } else {
          roomsData = rooms || [];
        }
      }

      // Create room lookup map
      const roomsMap = new Map(roomsData.map(room => [room.id, room]));

      // Get today's attendance records
      const { data: attendanceRecords, error: attendanceError } = await supabase
        .from("attendance_records")
        .select("student_id, status")
        .eq("school_id", schoolId)
        .gte("timestamp", today + "T00:00:00")
        .lte("timestamp", today + "T23:59:59");

      if (attendanceError) throw attendanceError;

      // Create a set of present/late/excused student IDs
      const presentStudentIds = new Set(
        (attendanceRecords || [])
          .filter(record => ["present", "late", "excused"].includes(record.status))
          .map(record => record.student_id)
      );

      // Filter absent students
      const absentStudents = allStudents
        .filter(student => !presentStudentIds.has(student.id))
        .map(student => {
          const room = roomsMap.get(student.room_id);
          return {
            ...student,
            room_name: room?.name || "Unknown Room",
          };
        });

      return absentStudents;
    } catch (error) {
      console.error("Error getting absent students:", error);
      return [];
    }
  }

  /**
   * Store reminder sent record for late marking
   */
  private async storeReminderSentRecord(schoolId: string, sentAt: Date): Promise<void> {
    try {
      await supabase
        .from("reminder_sent_records")
        .upsert({
          school_id: schoolId,
          sent_at: sentAt.toISOString(),
          date: sentAt.toISOString().split('T')[0],
        }, {
          onConflict: 'school_id,date'
        });
    } catch (error) {
      console.error("Error storing reminder sent record:", error);
    }
  }

  /**
   * Update reminder schedule for a school
   */
  private async updateReminderSchedule(schoolId: string): Promise<void> {
    // This would be called when settings change
    // For now, we recalculate on each check
  }

  /**
   * Clean up old sent reminders
   */
  private cleanupSentReminders(today: string): void {
    const keysToDelete = Array.from(this.sentReminders).filter(key => !key.startsWith(today));
    keysToDelete.forEach(key => this.sentReminders.delete(key));
  }

  /**
   * Get service status
   */
  public getStatus(): {
    isRunning: boolean;
    sentRemindersToday: number;
    nextCheck?: Date;
  } {
    const today = new Date().toISOString().split('T')[0];
    const sentToday = Array.from(this.sentReminders).filter(key => key.startsWith(today)).length;
    
    return {
      isRunning: this.isRunning,
      sentRemindersToday: sentToday,
      nextCheck: this.isRunning ? new Date(Date.now() + 60000) : undefined,
    };
  }
}

// Export singleton instance
export const automatedReminderService = AutomatedReminderService.getInstance();

// Auto-start the service in production
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  automatedReminderService.start();
}
