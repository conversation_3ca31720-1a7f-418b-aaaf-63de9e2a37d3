import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Building2,
  Home,
  Plus,
  Edit,
  Trash2,
  Users,
  MoreVertical,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useTranslation } from "react-i18next";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { toast as sonnerToast } from "sonner";

interface Block {
  id: string;
  name: string;
  school_id: string;
  created_at: string;
  roomCount: number;
}

interface Room {
  id: string;
  name: string;
  building?: string;
  floor?: string;
  capacity?: number;
  block_id: string;
  school_id: string;
  created_at: string;
  blocks?: {
    name: string;
  };
}

export default function BlockRoomManagement() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { profile } = useAuth();
  
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedBlock, setSelectedBlock] = useState<string | null>(null);
  
  // Dialog states
  const [blockDialogOpen, setBlockDialogOpen] = useState(false);
  const [roomDialogOpen, setRoomDialogOpen] = useState(false);
  const [editingBlock, setEditingBlock] = useState<Block | null>(null);
  const [editingRoom, setEditingRoom] = useState<Room | null>(null);
  
  // Form states
  const [blockForm, setBlockForm] = useState({
    name: "",
  });
  const [roomForm, setRoomForm] = useState({
    name: "",
    building: "",
    floor: "",
    capacity: "",
    block_id: "",
  });
  
  const [saving, setSaving] = useState(false);

  // Fetch blocks and rooms
  const fetchData = async () => {
    if (!profile?.school_id) return;

    try {
      setLoading(true);

      // Fetch blocks
      const { data: blocksData, error: blocksError } = await supabase
        .from("blocks")
        .select("*")
        .eq("school_id", profile.school_id)
        .order("name");

      if (blocksError) throw blocksError;

      // Fetch rooms with block info
      const { data: roomsData, error: roomsError } = await supabase
        .from("rooms")
        .select(`
          *,
          blocks (
            name
          )
        `)
        .eq("school_id", profile.school_id)
        .order("name");

      if (roomsError) throw roomsError;

      // Count rooms per block
      const blocksWithRoomCount = (blocksData || []).map(block => ({
        ...block,
        roomCount: (roomsData || []).filter(room => room.block_id === block.id).length,
      }));

      setBlocks(blocksWithRoomCount);
      setRooms(roomsData || []);
    } catch (error) {
      console.error("Error fetching data:", error);
      toast({
        title: t("common.error"),
        description: t("admin.attendanceManagement.fetchDataError"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Create or update block
  const saveBlock = async () => {
    if (!profile?.school_id || !blockForm.name.trim()) return;

    try {
      setSaving(true);

      const blockData = {
        name: blockForm.name.trim(),
        school_id: profile.school_id,
      };

      if (editingBlock) {
        // Update existing block
        const { error } = await supabase
          .from("blocks")
          .update(blockData)
          .eq("id", editingBlock.id);

        if (error) throw error;

        sonnerToast.success(t("admin.attendanceManagement.blockUpdated"), {
          description: t("admin.attendanceManagement.blockUpdatedDescription", {
            name: blockForm.name,
          }),
        });
      } else {
        // Create new block
        const { error } = await supabase
          .from("blocks")
          .insert(blockData);

        if (error) throw error;

        sonnerToast.success(t("admin.attendanceManagement.blockCreated"), {
          description: t("admin.attendanceManagement.blockCreatedDescription", {
            name: blockForm.name,
          }),
        });
      }

      setBlockDialogOpen(false);
      setEditingBlock(null);
      setBlockForm({ name: "" });
      await fetchData();
    } catch (error) {
      console.error("Error saving block:", error);
      toast({
        title: t("common.error"),
        description: t("admin.attendanceManagement.saveBlockError"),
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Create or update room
  const saveRoom = async () => {
    if (!profile?.school_id || !roomForm.name.trim() || !roomForm.block_id) return;

    try {
      setSaving(true);

      const roomData = {
        name: roomForm.name.trim(),
        building: roomForm.building?.trim() || null,
        floor: roomForm.floor ? parseInt(roomForm.floor.toString()) : null,
        capacity: roomForm.capacity ? parseInt(roomForm.capacity.toString()) : null,
        block_id: roomForm.block_id,
        school_id: profile.school_id,
      };

      if (editingRoom) {
        // Update existing room
        const { error } = await supabase
          .from("rooms")
          .update(roomData)
          .eq("id", editingRoom.id);

        if (error) throw error;

        sonnerToast.success(t("admin.attendanceManagement.roomUpdated"), {
          description: t("admin.attendanceManagement.roomUpdatedDescription", {
            name: roomForm.name,
          }),
        });
      } else {
        // Create new room
        const { error } = await supabase
          .from("rooms")
          .insert(roomData);

        if (error) throw error;

        sonnerToast.success(t("admin.attendanceManagement.roomCreated"), {
          description: t("admin.attendanceManagement.roomCreatedDescription", {
            name: roomForm.name,
          }),
        });
      }

      setRoomDialogOpen(false);
      setEditingRoom(null);
      setRoomForm({ name: "", building: "", floor: "", capacity: "", block_id: "" });
      await fetchData();
    } catch (error) {
      console.error("Error saving room:", error);
      toast({
        title: t("common.error"),
        description: t("admin.attendanceManagement.saveRoomError"),
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Delete block
  const deleteBlock = async (block: Block) => {
    try {
      setSaving(true);

      // First get all rooms in this block from the database (fresh data)
      const { data: blockRooms, error: roomsQueryError } = await supabase
        .from("rooms")
        .select("id, name, block_id")
        .eq("block_id", block.id);

      if (roomsQueryError) {
        console.error("Error fetching rooms for block:", roomsQueryError);
        throw roomsQueryError;
      }

      console.log(`Deleting block ${block.name} with ${blockRooms?.length || 0} rooms:`, blockRooms?.map(r => r.name) || []);

      for (const room of blockRooms || []) {
        console.log(`Starting deletion of room: ${room.name} (${room.id})`);
        // 1. Update profiles to remove room assignment (set room_id to null)
        try {
          await supabase
            .from("profiles")
            .update({ room_id: null })
            .eq("room_id", room.id);
        } catch (e) {
          console.warn("Could not update profiles for room:", room.id);
        }

        // 2. Delete room locations
        try {
          await supabase
            .from("room_locations")
            .delete()
            .eq("room_id", room.id);
        } catch (e) {
          console.warn("Could not delete room_locations for room:", room.id);
        }

        // 3. Delete location verification settings
        try {
          await supabase
            .from("location_verification_settings")
            .delete()
            .eq("room_id", room.id);
        } catch (e) {
          console.warn("Could not delete location_verification_settings for room:", room.id);
        }

        // 4. Delete attendance records for this room
        try {
          await supabase
            .from("attendance_records")
            .delete()
            .eq("room_id", room.id);
        } catch (e) {
          console.warn("Could not delete attendance_records for room:", room.id);
        }

        // 5. Delete any QR sessions for this room
        try {
          await supabase
            .from("qr_sessions")
            .delete()
            .eq("room_id", room.id);
        } catch (e) {
          console.warn("Could not delete qr_sessions for room:", room.id);
        }

        // 6. Delete any excuses for this room
        try {
          await supabase
            .from("excuses")
            .delete()
            .eq("room_id", room.id);
        } catch (e) {
          console.warn("Could not delete excuses for room:", room.id);
        }

        // 7. Delete any courses for this room
        try {
          await supabase
            .from("courses")
            .delete()
            .eq("room_id", room.id);
        } catch (e) {
          console.warn("Could not delete courses for room:", room.id);
        }

        // 8. Delete any attendance alerts for this room
        try {
          await supabase
            .from("attendance_alerts")
            .delete()
            .eq("room_id", room.id);
        } catch (e) {
          console.warn("Could not delete attendance_alerts for room:", room.id);
        }

        // 9. Delete the room itself - this MUST succeed
        const { error: roomError } = await supabase
          .from("rooms")
          .delete()
          .eq("id", room.id);

        if (roomError) {
          console.error("CRITICAL: Failed to delete room:", room.id, roomError);
          throw new Error(`Failed to delete room ${room.name}: ${roomError.message}`);
        }

        console.log(`Successfully deleted room: ${room.name} (${room.id})`);
      }

      // Now delete data that references the BLOCK directly (not through rooms)
      console.log(`Deleting block-level references for block: ${block.name}`);

      // 1. Update profiles to remove block assignment (set block_id to null)
      try {
        await supabase
          .from("profiles")
          .update({ block_id: null })
          .eq("block_id", block.id);
        console.log("Updated profiles to remove block assignment");
      } catch (e) {
        console.warn("Could not update profiles for block:", block.id);
      }

      // 2. Delete location verification settings for this block
      try {
        await supabase
          .from("location_verification_settings")
          .delete()
          .eq("block_id", block.id);
        console.log("Deleted location verification settings for block");
      } catch (e) {
        console.warn("Could not delete location_verification_settings for block:", block.id);
      }

      // 3. Delete attendance records for this block
      try {
        await supabase
          .from("attendance_records")
          .delete()
          .eq("block_id", block.id);
        console.log("Deleted attendance records for block");
      } catch (e) {
        console.warn("Could not delete attendance_records for block:", block.id);
      }

      // 4. Delete QR sessions for this block
      try {
        await supabase
          .from("qr_sessions")
          .delete()
          .eq("block_id", block.id);
        console.log("Deleted QR sessions for block");
      } catch (e) {
        console.warn("Could not delete qr_sessions for block:", block.id);
      }

      // Verify all rooms are deleted before deleting the block
      const { data: remainingRooms, error: checkError } = await supabase
        .from("rooms")
        .select("id")
        .eq("block_id", block.id);

      if (checkError) {
        console.error("Error checking remaining rooms:", checkError);
      }

      if (remainingRooms && remainingRooms.length > 0) {
        throw new Error(`Cannot delete block: ${remainingRooms.length} rooms still exist in this block`);
      }

      // Finally delete the block
      const { error } = await supabase
        .from("blocks")
        .delete()
        .eq("id", block.id);

      if (error) throw error;

      sonnerToast.success(t("admin.attendanceManagement.blockDeleted"), {
        description: t("admin.attendanceManagement.blockDeletedDescription", {
          name: block.name,
        }),
      });

      await fetchData();
    } catch (error) {
      console.error("Error deleting block:", error);
      toast({
        title: t("common.error"),
        description: t("admin.attendanceManagement.deleteBlockError"),
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Delete room
  const deleteRoom = async (room: Room) => {
    try {
      setSaving(true);

      // 1. Update profiles to remove room assignment (set room_id to null)
      try {
        await supabase
          .from("profiles")
          .update({ room_id: null })
          .eq("room_id", room.id);
      } catch (e) {
        console.warn("Could not update profiles for room:", room.id);
      }

      // 2. Delete room locations
      try {
        await supabase
          .from("room_locations")
          .delete()
          .eq("room_id", room.id);
      } catch (e) {
        console.warn("Could not delete room_locations for room:", room.id);
      }

      // 3. Delete location verification settings
      try {
        await supabase
          .from("location_verification_settings")
          .delete()
          .eq("room_id", room.id);
      } catch (e) {
        console.warn("Could not delete location_verification_settings for room:", room.id);
      }

      // 4. Delete attendance records for this room
      try {
        await supabase
          .from("attendance_records")
          .delete()
          .eq("room_id", room.id);
      } catch (e) {
        console.warn("Could not delete attendance_records for room:", room.id);
      }

      // 5. Delete any QR sessions for this room
      try {
        await supabase
          .from("qr_sessions")
          .delete()
          .eq("room_id", room.id);
      } catch (e) {
        console.warn("Could not delete qr_sessions for room:", room.id);
      }

      // 6. Delete any excuses for this room
      try {
        await supabase
          .from("excuses")
          .delete()
          .eq("room_id", room.id);
      } catch (e) {
        console.warn("Could not delete excuses for room:", room.id);
      }

      // 7. Delete any courses for this room
      try {
        await supabase
          .from("courses")
          .delete()
          .eq("room_id", room.id);
      } catch (e) {
        console.warn("Could not delete courses for room:", room.id);
      }

      // 8. Delete any attendance alerts for this room
      try {
        await supabase
          .from("attendance_alerts")
          .delete()
          .eq("room_id", room.id);
      } catch (e) {
        console.warn("Could not delete attendance_alerts for room:", room.id);
      }

      // Finally delete the room
      const { error } = await supabase
        .from("rooms")
        .delete()
        .eq("id", room.id);

      if (error) throw error;

      sonnerToast.success(t("admin.attendanceManagement.roomDeleted"), {
        description: t("admin.attendanceManagement.roomDeletedDescription", {
          name: room.name,
        }),
      });

      await fetchData();
    } catch (error) {
      console.error("Error deleting room:", error);
      toast({
        title: t("common.error"),
        description: t("admin.attendanceManagement.deleteRoomError"),
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Open edit dialogs
  const openEditBlock = (block: Block) => {
    setEditingBlock(block);
    setBlockForm({
      name: block.name,
    });
    setBlockDialogOpen(true);
  };

  const openEditRoom = (room: Room) => {
    setEditingRoom(room);
    setRoomForm({
      name: room.name,
      building: room.building || "",
      floor: room.floor || "",
      capacity: room.capacity?.toString() || "",
      block_id: room.block_id,
    });
    setRoomDialogOpen(true);
  };

  // Open create dialogs
  const openCreateBlock = () => {
    setEditingBlock(null);
    setBlockForm({ name: "" });
    setBlockDialogOpen(true);
  };

  const openCreateRoom = (blockId?: string) => {
    setEditingRoom(null);
    setRoomForm({
      name: "",
      building: "",
      floor: "",
      capacity: "",
      block_id: blockId || "",
    });
    setRoomDialogOpen(true);
  };

  useEffect(() => {
    fetchData();
  }, [profile?.school_id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner message={t("admin.attendanceManagement.loadingStructure")} />
      </div>
    );
  }

  const filteredRooms = selectedBlock 
    ? rooms.filter(room => room.block_id === selectedBlock)
    : rooms;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-lg font-semibold">
            {t("admin.attendanceManagement.manageStructure")}
          </h3>
          <p className="text-sm text-muted-foreground">
            {t("admin.attendanceManagement.manageStructureDescription")}
          </p>
        </div>
        <Button onClick={openCreateBlock} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          {t("admin.attendanceManagement.addBlock")}
        </Button>
      </div>

      {/* Blocks Section */}
      <div className="space-y-4">
        <h4 className="text-md font-medium flex items-center gap-2">
          <Building2 className="h-4 w-4" />
          {t("admin.attendanceManagement.blocks")} ({blocks.length})
        </h4>
        
        {blocks.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-8">
              <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-muted-foreground text-center">
                {t("admin.attendanceManagement.noBlocks")}
              </p>
              <Button onClick={openCreateBlock} className="mt-4">
                {t("admin.attendanceManagement.createFirstBlock")}
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {blocks.map((block) => (
              <Card key={block.id} className="relative">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-base truncate">
                        {block.name}
                      </CardTitle>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => openEditBlock(block)}>
                          <Edit className="h-4 w-4 mr-2" />
                          {t("common.edit")}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => openCreateRoom(block.id)}>
                          <Plus className="h-4 w-4 mr-2" />
                          {t("admin.attendanceManagement.addRoom")}
                        </DropdownMenuItem>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                              <Trash2 className="h-4 w-4 mr-2" />
                              {t("common.delete")}
                            </DropdownMenuItem>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>
                                {t("admin.attendanceManagement.deleteBlockTitle")}
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                {t("admin.attendanceManagement.deleteBlockDescription", {
                                  name: block.name,
                                })}
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => deleteBlock(block)}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              >
                                {t("common.delete")}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Home className="h-4 w-4" />
                      <span>
                        {t("admin.attendanceManagement.roomCount", {
                          count: block.roomCount,
                        })}
                      </span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedBlock(
                        selectedBlock === block.id ? null : block.id
                      )}
                    >
                      {selectedBlock === block.id 
                        ? t("common.hideRooms") 
                        : t("common.showRooms")
                      }
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Rooms Section */}
      {(selectedBlock || rooms.length > 0) && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-md font-medium flex items-center gap-2">
              <Home className="h-4 w-4" />
              {selectedBlock 
                ? t("admin.attendanceManagement.roomsInBlock", {
                    blockName: blocks.find(b => b.id === selectedBlock)?.name || "",
                  })
                : t("admin.attendanceManagement.allRooms")
              } ({filteredRooms.length})
            </h4>
            <Button 
              onClick={() => openCreateRoom(selectedBlock || undefined)} 
              size="sm"
              disabled={blocks.length === 0}
            >
              <Plus className="h-4 w-4 mr-2" />
              {t("admin.attendanceManagement.addRoom")}
            </Button>
          </div>

          {filteredRooms.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-8">
                <Home className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground text-center">
                  {selectedBlock 
                    ? t("admin.attendanceManagement.noRoomsInBlock")
                    : t("admin.attendanceManagement.noRooms")
                  }
                </p>
                {blocks.length > 0 && (
                  <Button 
                    onClick={() => openCreateRoom(selectedBlock || undefined)} 
                    className="mt-4"
                  >
                    {t("admin.attendanceManagement.createFirstRoom")}
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredRooms.map((room) => (
                <Card key={room.id}>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-base truncate">
                          {room.name}
                        </CardTitle>
                        <p className="text-sm text-muted-foreground">
                          {room.blocks?.name}
                        </p>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => openEditRoom(room)}>
                            <Edit className="h-4 w-4 mr-2" />
                            {t("common.edit")}
                          </DropdownMenuItem>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                <Trash2 className="h-4 w-4 mr-2" />
                                {t("common.delete")}
                              </DropdownMenuItem>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>
                                  {t("admin.attendanceManagement.deleteRoomTitle")}
                                </AlertDialogTitle>
                                <AlertDialogDescription>
                                  {t("admin.attendanceManagement.deleteRoomDescription", {
                                    name: room.name,
                                  })}
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => deleteRoom(room)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  {t("common.delete")}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      {room.building && (
                        <div className="flex items-center gap-2">
                          <Building2 className="h-3 w-3 text-muted-foreground" />
                          <span>{room.building}</span>
                        </div>
                      )}
                      {room.floor && (
                        <div className="flex items-center gap-2">
                          <span className="text-muted-foreground">
                            {t("admin.attendanceManagement.floor")}:
                          </span>
                          <span>{room.floor}</span>
                        </div>
                      )}
                      {room.capacity && (
                        <div className="flex items-center gap-2">
                          <Users className="h-3 w-3 text-muted-foreground" />
                          <span>
                            {t("admin.attendanceManagement.capacity")}: {room.capacity}
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Block Dialog */}
      <Dialog open={blockDialogOpen} onOpenChange={setBlockDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {editingBlock 
                ? t("admin.attendanceManagement.editBlock")
                : t("admin.attendanceManagement.createBlock")
              }
            </DialogTitle>
            <DialogDescription>
              {editingBlock 
                ? t("admin.attendanceManagement.editBlockDescription")
                : t("admin.attendanceManagement.createBlockDescription")
              }
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="block-name">
                {t("admin.attendanceManagement.blockName")} *
              </Label>
              <Input
                id="block-name"
                value={blockForm.name}
                onChange={(e) => setBlockForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder={t("admin.attendanceManagement.blockNamePlaceholder")}
              />
            </div>

          </div>
          <DialogFooter>
            <Button
              type="submit"
              onClick={saveBlock}
              disabled={!blockForm.name.trim() || saving}
            >
              {saving ? t("common.saving") : (editingBlock ? t("common.update") : t("common.create"))}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Room Dialog */}
      <Dialog open={roomDialogOpen} onOpenChange={setRoomDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {editingRoom 
                ? t("admin.attendanceManagement.editRoom")
                : t("admin.attendanceManagement.createRoom")
              }
            </DialogTitle>
            <DialogDescription>
              {editingRoom 
                ? t("admin.attendanceManagement.editRoomDescription")
                : t("admin.attendanceManagement.createRoomDescription")
              }
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="room-name">
                {t("admin.attendanceManagement.roomName")} *
              </Label>
              <Input
                id="room-name"
                value={roomForm.name}
                onChange={(e) => setRoomForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder={t("admin.attendanceManagement.roomNamePlaceholder")}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="room-block">
                {t("admin.attendanceManagement.selectBlock")} *
              </Label>
              <select
                id="room-block"
                value={roomForm.block_id}
                onChange={(e) => setRoomForm(prev => ({ ...prev, block_id: e.target.value }))}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="">
                  {t("admin.attendanceManagement.selectBlockPlaceholder")}
                </option>
                {blocks.map((block) => (
                  <option key={block.id} value={block.id}>
                    {block.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="room-building">
                  {t("admin.attendanceManagement.building")}
                </Label>
                <Input
                  id="room-building"
                  value={roomForm.building}
                  onChange={(e) => setRoomForm(prev => ({ ...prev, building: e.target.value }))}
                  placeholder={t("admin.attendanceManagement.buildingPlaceholder")}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="room-floor">
                  {t("admin.attendanceManagement.floor")}
                </Label>
                <Input
                  id="room-floor"
                  type="number"
                  value={roomForm.floor}
                  onChange={(e) => setRoomForm(prev => ({ ...prev, floor: e.target.value }))}
                  placeholder={t("admin.attendanceManagement.floorPlaceholder")}
                  min="0"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="room-capacity">
                {t("admin.attendanceManagement.capacity")}
              </Label>
              <Input
                id="room-capacity"
                type="number"
                value={roomForm.capacity}
                onChange={(e) => setRoomForm(prev => ({ ...prev, capacity: e.target.value }))}
                placeholder={t("admin.attendanceManagement.capacityPlaceholder")}
                min="1"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="submit"
              onClick={saveRoom}
              disabled={!roomForm.name.trim() || !roomForm.block_id || saving}
            >
              {saving ? t("common.saving") : (editingRoom ? t("common.update") : t("common.create"))}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
