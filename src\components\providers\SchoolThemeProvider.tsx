import React, { createContext, useContext, useEffect, useState } from "react";
import { useSchool } from "@/context/SchoolContext";

interface SchoolThemeContextType {
  primaryColor: string;
  secondaryColor: string;
  logoUrl: string | null;
  applyTheme: (primaryColor: string, secondaryColor: string) => Promise<void>;
  setLogoUrl: (url: string | null) => void;
}

const SchoolThemeContext = createContext<SchoolThemeContextType>({
  primaryColor: "#4f46e5", // Default indigo
  secondaryColor: "#f97316", // Default orange
  logoUrl: null,
  applyTheme: () => Promise.resolve(),
  setLogoUrl: () => {},
});

export const useSchoolTheme = () => useContext(SchoolThemeContext);

interface SchoolThemeProviderProps {
  children: React.ReactNode;
}

export function SchoolThemeProvider({ children }: SchoolThemeProviderProps) {
  const { currentSchool } = useSchool();
  const [primaryColor, setPrimaryColor] = useState("#4f46e5");
  const [secondaryColor, setSecondaryColor] = useState("#f97316");
  const [logoUrl, setLogoUrl] = useState<string | null>(null);

  // Apply theme when school changes
  useEffect(() => {
    if (currentSchool) {
      // First set defaults from the school object
      const defaultPrimary = currentSchool.primaryColor || "#4f46e5";
      const defaultSecondary = currentSchool.secondaryColor || "#f97316";

      setPrimaryColor(defaultPrimary);
      setSecondaryColor(defaultSecondary);
      setLogoUrl(currentSchool.logoUrl || null);

      // Apply default theme immediately
      applyThemeToDocument(defaultPrimary, defaultSecondary);

      // Then try to load from the dedicated branding table
      loadBrandingFromDatabase(currentSchool.id);

      // Disabling these checks as they're causing CORS errors and page freezes
      // checkSystemThemeOverrides(currentSchool.id);
      // checkGlobalThemeSettings();

      // Set up real-time subscription for branding changes
      setupBrandingSubscription(currentSchool.id);

      // We're removing the periodic check interval as it's causing CORS errors and infinite loops
      // Real-time subscriptions should be sufficient for theme updates

      // Set up a listener for theme changes (light/dark mode toggle)
      const handleThemeChange = () => {
        // When theme changes, we need to reapply the appropriate theme
        if (currentSchool?.id) {
          // Instead of making API calls that might fail with CORS errors,
          // just reapply the current theme based on the current state
          applyThemeToDocument(primaryColor, secondaryColor);
        }
      };

      // Store the function reference for cleanup
      (window as any).__themeChangeObserver = handleThemeChange;

      // Disabling MutationObserver as it might be triggering problematic code
      // We'll rely on direct theme application instead
      (window as any).__themeModeObserver = null;

      // Set up listener for custom theme change events
      const handleSystemThemeChange = (event: CustomEvent) => {
        // Check if the override flag is set
        const overrideSchoolCustomizations =
          event.detail.overrideSchoolCustomizations !== false;

        if (!overrideSchoolCustomizations) {
          // If override is disabled, we need to check for school-specific branding
          // This allows school admins to customize their own themes
          if (currentSchool?.id) {
            loadBrandingFromDatabase(currentSchool.id);
          }
          return;
        }

        // Check if we're in the right theme mode to apply these changes
        const isDarkMode = document.documentElement.classList.contains("dark");
        const eventTheme = event.detail.theme;

        if (
          (isDarkMode && eventTheme === "darkTheme") ||
          (!isDarkMode && eventTheme === "lightTheme")
        ) {
          const colors = event.detail.colors;

          // Update state with the new theme data
          if (colors.primary) {
            setPrimaryColor(colors.primary);
          }

          if (colors.secondary) {
            setSecondaryColor(colors.secondary);
          }

          // Apply the theme immediately
          if (colors.primary && colors.secondary) {
            applyThemeToDocument(colors.primary, colors.secondary);
          }

          // Force a refresh of the UI
          const root = document.documentElement;
          root.classList.add("theme-refreshing");
          setTimeout(() => {
            root.classList.remove("theme-refreshing");
          }, 100);
        }
      };

      // Add the event listener
      window.addEventListener(
        "system-theme-changed",
        handleSystemThemeChange as EventListener
      );

      // Store the function reference for cleanup
      (window as any).__themeChangeListener = handleSystemThemeChange;
    }

    // Cleanup function to remove subscription, event listener, observer, and interval when component unmounts or school changes
    return () => {
      cleanupBrandingSubscription();

      // Remove the event listener using the stored reference
      if ((window as any).__themeChangeListener) {
        window.removeEventListener(
          "system-theme-changed",
          (window as any).__themeChangeListener as EventListener
        );
        (window as any).__themeChangeListener = null;
      }

      // Clear any theme check interval that might exist
      if ((window as any).__themeCheckInterval) {
        clearInterval((window as any).__themeCheckInterval);
        (window as any).__themeCheckInterval = null;
      }

      // Disconnect the theme mode observer
      if ((window as any).__themeModeObserver) {
        (window as any).__themeModeObserver.disconnect();
        (window as any).__themeModeObserver = null;
      }

      // Remove any theme override styles
      const darkStyle = document.getElementById("dark-theme-override-style");
      if (darkStyle) {
        darkStyle.remove();
      }

      const lightStyle = document.getElementById("light-theme-override-style");
      if (lightStyle) {
        lightStyle.remove();
      }
    };
  }, [currentSchool]);

  // Check for global theme settings that apply to all schools
  const checkGlobalThemeSettings = async () => {
    try {
      // Import supabase client
      const { supabase } = await import("@/lib/supabase");

      // Check for global theme settings
      const { data: globalSettings, error: globalError } = await supabase
        .from("system_settings")
        .select("*")
        .eq("setting_name", "global_theme_settings")
        .maybeSingle();

      // If we get a CORS error or network error, don't log it to avoid console spam
      if (
        globalError &&
        !(
          globalError.message?.includes("fetch") ||
          globalError.message?.includes("network") ||
          globalError.message?.includes("CORS")
        )
      ) {
        console.log("SchoolThemeProvider: Global theme settings check:", {
          globalSettings,
          globalError,
        });
      }

      if (globalSettings && !globalError) {
        // We have global theme settings

        // Check if the override should be applied (respecting the override flag)
        const shouldOverride =
          globalSettings.setting_value.overrideSchoolCustomizations !== false;

        if (shouldOverride) {
          // Get the current theme (light or dark)
          const isDarkMode =
            document.documentElement.classList.contains("dark");
          const themeKey = isDarkMode ? "darkTheme" : "lightTheme";

          // Extract the theme colors for the current theme
          const themeColors = globalSettings.setting_value[themeKey];

          if (themeColors) {
            // Update state with the new theme data
            if (themeColors.primary) {
              setPrimaryColor(themeColors.primary);
            }

            if (themeColors.secondary) {
              setSecondaryColor(themeColors.secondary);
            }

            // Apply the theme immediately
            if (themeColors.primary && themeColors.secondary) {
              applyThemeToDocument(themeColors.primary, themeColors.secondary);
            }
          }
        } else {
          // Global settings will not be applied
        }
      }
    } catch (error) {
      console.error(
        "SchoolThemeProvider: Error checking global theme settings:",
        error
      );
    }
  };

  // Check for system-wide theme settings that might override school settings
  const checkSystemThemeOverrides = async (schoolId: string) => {
    try {
      // Import supabase client
      const { supabase } = await import("@/lib/supabase");

      // Check for school-specific theme overrides
      const { data: overrideData, error: overrideError } = await supabase
        .from("system_school_settings_overrides")
        .select("*")
        .eq("school_id", schoolId)
        .eq("setting_name", "theme_settings")
        .maybeSingle();

      // If we get a CORS error or network error, don't log it to avoid console spam
      if (
        overrideError &&
        !(
          overrideError.message?.includes("fetch") ||
          overrideError.message?.includes("network") ||
          overrideError.message?.includes("CORS")
        )
      ) {
        console.log("SchoolThemeProvider: System theme override check:", {
          overrideData,
          overrideError,
        });
      }

      if (overrideData && !overrideError) {
        // We have a system override for this school

        // Check if the override should be applied (respecting the override flag and override_enabled)
        const shouldOverride =
          overrideData.override_enabled === true &&
          overrideData.setting_value.overrideSchoolCustomizations !== false;

        if (shouldOverride) {
          // Get the current theme (light or dark)
          const isDarkMode =
            document.documentElement.classList.contains("dark");
          const themeKey = isDarkMode ? "darkTheme" : "lightTheme";

          // Extract the theme colors for the current theme
          const themeColors = overrideData.setting_value[themeKey];

          if (themeColors) {
            // Update state with the new theme data
            if (themeColors.primary) {
              setPrimaryColor(themeColors.primary);
            }

            if (themeColors.secondary) {
              setSecondaryColor(themeColors.secondary);
            }

            // Apply the theme immediately
            if (themeColors.primary && themeColors.secondary) {
              applyThemeToDocument(themeColors.primary, themeColors.secondary);
            }

            // Store both light and dark theme settings in window for quick access during theme switches
            if (!window.__themeSettings) {
              window.__themeSettings = {};
            }

            // Store the current theme settings
            window.__themeSettings.currentOverride = overrideData.setting_value;
          }
        } else {
          // If override is disabled, we need to check for school-specific branding
          // This allows school admins to customize their own themes
          await loadBrandingFromDatabase(schoolId);
        }
      }
    } catch (error) {
      console.error(
        "SchoolThemeProvider: Error checking system theme overrides:",
        error
      );
    }
  };

  // Set up real-time subscription to the school_branding table only
  const setupBrandingSubscription = async (schoolId: string) => {
    try {
      const { supabase } = await import("@/lib/supabase");

      // Create a simplified subscription to just the school_branding table
      const brandingSubscription = supabase
        .channel("school-branding-changes")
        .on(
          "postgres_changes",
          {
            event: "*",
            schema: "public",
            table: "school_branding",
            filter: `school_id=eq.${schoolId}`,
          },
          (payload) => {
            // Update the theme with the new branding data
            if (payload.new) {
              const newBranding = payload.new as any;

              // Update state with the new branding data
              if (newBranding.primary_color) {
                setPrimaryColor(newBranding.primary_color);
              }

              if (newBranding.secondary_color) {
                setSecondaryColor(newBranding.secondary_color);
              }

              if (newBranding.logo_url !== undefined) {
                setLogoUrl(newBranding.logo_url);
              }

              // Apply the theme
              if (newBranding.primary_color && newBranding.secondary_color) {
                applyThemeToDocument(
                  newBranding.primary_color,
                  newBranding.secondary_color
                );
              }
            }
          }
        )
        .subscribe();

      // Store the subscription for cleanup
      (window as any).__brandingSubscription = brandingSubscription;
    } catch (error) {
      console.error("Error setting up branding subscription:", error);
    }
  };

  // Cleanup function to remove the subscription
  const cleanupBrandingSubscription = async () => {
    try {
      if ((window as any).__brandingSubscription) {
        const { supabase } = await import("@/lib/supabase");
        supabase.removeChannel((window as any).__brandingSubscription);
        (window as any).__brandingSubscription = null;
      }

      // Remove the theme change observer
      if ((window as any).__themeChangeObserver) {
        document.removeEventListener(
          "themeChange",
          (window as any).__themeChangeObserver
        );
        (window as any).__themeChangeObserver = null;
      }

      // Clear any theme check interval
      if ((window as any).__themeCheckInterval) {
        clearInterval((window as any).__themeCheckInterval);
        (window as any).__themeCheckInterval = null;
      }
    } catch (error) {
      console.error("Error cleaning up branding subscription:", error);
    }
  };

  // Load branding data from the dedicated school_branding table
  const loadBrandingFromDatabase = async (schoolId: string) => {
    try {
      // Import supabase client
      const { supabase } = await import("@/lib/supabase");

      // Try to get branding data from the dedicated table
      const { data: brandingData, error: brandingError } = await supabase
        .from("school_branding")
        .select("*")
        .eq("school_id", schoolId)
        .maybeSingle();

      if (brandingData && !brandingError) {
        // Update state with the branding data
        const primary = brandingData.primary_color || primaryColor;
        const secondary = brandingData.secondary_color || secondaryColor;

        setPrimaryColor(primary);
        setSecondaryColor(secondary);

        if (brandingData.logo_url) {
          setLogoUrl(brandingData.logo_url);
        }

        // Apply the theme
        applyThemeToDocument(primary, secondary);
      } else if (brandingError && brandingError.code !== "PGRST116") {
        // PGRST116 is "not found" error, which is expected if no record exists yet
        console.error(
          "SchoolThemeProvider: Error loading branding data:",
          brandingError
        );
      }
    } catch (error) {
      console.error(
        "SchoolThemeProvider: Error in loadBrandingFromDatabase:",
        error
      );
    }
  };

  // Function to apply theme colors to CSS variables
  const applyThemeToDocument = (primary: string, secondary: string) => {
    try {
      // Check if we're in dark or light mode
      const isDarkMode = document.documentElement.classList.contains("dark");

      // Use the local hexToHSL function for immediate application
      // Convert hex to HSL for CSS variables
      const primaryHsl = hexToHSL(primary);
      const secondaryHsl = hexToHSL(secondary);

      // Apply to document root
      document.documentElement.style.setProperty("--primary", primaryHsl);
      document.documentElement.style.setProperty("--secondary", secondaryHsl);

      // Apply to other elements that might need direct color values
      document.documentElement.style.setProperty("--primary-color", primary);
      document.documentElement.style.setProperty(
        "--secondary-color",
        secondary
      );

      // Update theme-color meta tag for mobile browsers
      const metaThemeColor = document.querySelector('meta[name="theme-color"]');
      if (metaThemeColor) {
        metaThemeColor.setAttribute("content", primary);
      }

      // Force a CSS refresh
      document.documentElement.classList.add("theme-refreshing");
      setTimeout(() => {
        document.documentElement.classList.remove("theme-refreshing");
      }, 100);

      // First, remove any previous override styles to ensure clean state
      const previousDarkStyle = document.getElementById(
        "dark-theme-override-style"
      );
      if (previousDarkStyle) {
        previousDarkStyle.remove();
      }

      const previousLightStyle = document.getElementById(
        "light-theme-override-style"
      );
      if (previousLightStyle) {
        previousLightStyle.remove();
      }

      // If we have stored theme settings, use them to create both light and dark mode styles
      if (window.__themeSettings && window.__themeSettings.currentOverride) {
        const storedSettings = window.__themeSettings.currentOverride;

        // Create dark mode style using the dark theme colors
        if (storedSettings.darkTheme && storedSettings.darkTheme.primary) {
          const darkPrimary = storedSettings.darkTheme.primary;

          const darkStyle = document.createElement("style");
          darkStyle.textContent = `
            .dark .bg-primary { background-color: ${darkPrimary} !important; }
            .dark .text-primary:not(button):not(.btn):not(button *):not(.btn *):not(svg):not(svg *) { color: ${darkPrimary} !important; }
            .dark .border-primary { border-color: ${darkPrimary} !important; }
            .dark .hover\\:bg-primary:hover { background-color: ${darkPrimary} !important; }
            .dark nav, .dark nav.bg-primary { background-color: ${darkPrimary} !important; }

            /* Additional selectors to ensure elements get the theme */
            .dark [class*="bg-primary"]:not(button):not(.btn) { background-color: ${darkPrimary} !important; }
            .dark [class*="text-primary"]:not(button):not(.btn):not(button *):not(.btn *):not(svg):not(svg *) { color: ${darkPrimary} !important; }
            .dark [class*="border-primary"] { border-color: ${darkPrimary} !important; }

            /* Force buttons with primary class to use the theme color for background only */
            .dark button.bg-primary, .dark .btn-primary {
              background-color: ${darkPrimary} !important;
              border-color: ${darkPrimary} !important;
              color: white !important;
            }

            /* Ensure text inside buttons remains visible */
            .dark button.bg-primary *, .dark .btn-primary * {
              color: white !important;
            }

            /* Icon fixes are now handled in icon-fix.css */

            /* Force links with primary class to use the theme color */
            .dark a.text-primary { color: ${darkPrimary} !important; }

            /* Force SVG icons with primary class to use the theme color, but preserve others */
            .dark svg.text-primary { color: ${darkPrimary} !important; fill: ${darkPrimary} !important; }
          `;

          // Add the dark mode style
          darkStyle.id = "dark-theme-override-style";
          document.head.appendChild(darkStyle);
        }

        // Create light mode style using the light theme colors
        if (storedSettings.lightTheme && storedSettings.lightTheme.primary) {
          const lightPrimary = storedSettings.lightTheme.primary;

          const lightStyle = document.createElement("style");
          lightStyle.textContent = `
            :not(.dark) .bg-primary { background-color: ${lightPrimary} !important; }
            :not(.dark) .text-primary:not(button):not(.btn):not(button *):not(.btn *):not(svg):not(svg *) { color: ${lightPrimary} !important; }
            :not(.dark) .border-primary { border-color: ${lightPrimary} !important; }
            :not(.dark) .hover\\:bg-primary:hover { background-color: ${lightPrimary} !important; }
            :not(.dark) nav, :not(.dark) nav.bg-primary { background-color: ${lightPrimary} !important; }

            /* Additional selectors to ensure elements get the theme */
            :not(.dark) [class*="bg-primary"]:not(button):not(.btn) { background-color: ${lightPrimary} !important; }
            :not(.dark) [class*="text-primary"]:not(button):not(.btn):not(button *):not(.btn *):not(svg):not(svg *) { color: ${lightPrimary} !important; }
            :not(.dark) [class*="border-primary"] { border-color: ${lightPrimary} !important; }

            /* Force buttons with primary class to use the theme color for background only */
            :not(.dark) button.bg-primary, :not(.dark) .btn-primary {
              background-color: ${lightPrimary} !important;
              border-color: ${lightPrimary} !important;
              color: white !important;
            }

            /* Ensure text inside buttons remains visible */
            :not(.dark) button.bg-primary *, :not(.dark) .btn-primary * {
              color: white !important;
            }

            /* Icon fixes are now handled in icon-fix.css */

            /* Force links with primary class to use the theme color */
            :not(.dark) a.text-primary { color: ${lightPrimary} !important; }

            /* Force SVG icons with primary class to use the theme color, but preserve others */
            :not(.dark) svg.text-primary { color: ${lightPrimary} !important; fill: ${lightPrimary} !important; }
          `;

          // Add the light mode style
          lightStyle.id = "light-theme-override-style";
          document.head.appendChild(lightStyle);
        }
      } else {
        // If we don't have stored settings, create styles based on the current theme

        // Create separate style sheets for dark and light mode
        if (isDarkMode) {
          // Dark mode styles - only applied when .dark class is present
          const darkStyle = document.createElement("style");
          darkStyle.textContent = `
            .dark .bg-primary { background-color: ${primary} !important; }
            .dark .text-primary:not(button):not(.btn):not(button *):not(.btn *):not(svg):not(svg *) { color: ${primary} !important; }
            .dark .border-primary { border-color: ${primary} !important; }
            .dark .hover\\:bg-primary:hover { background-color: ${primary} !important; }
            .dark nav, .dark nav.bg-primary { background-color: ${primary} !important; }

            /* Additional selectors to ensure elements get the theme */
            .dark [class*="bg-primary"]:not(button):not(.btn) { background-color: ${primary} !important; }
            .dark [class*="text-primary"]:not(button):not(.btn):not(button *):not(.btn *):not(svg):not(svg *) { color: ${primary} !important; }
            .dark [class*="border-primary"] { border-color: ${primary} !important; }

            /* Force buttons with primary class to use the theme color for background only */
            .dark button.bg-primary, .dark .btn-primary {
              background-color: ${primary} !important;
              border-color: ${primary} !important;
              color: white !important;
            }

            /* Ensure text inside buttons remains visible */
            .dark button.bg-primary *, .dark .btn-primary * {
              color: white !important;
            }

            /* Icon fixes are now handled in icon-fix.css */

            /* Force links with primary class to use the theme color */
            .dark a.text-primary { color: ${primary} !important; }

            /* Force SVG icons with primary class to use the theme color, but preserve others */
            .dark svg.text-primary { color: ${primary} !important; fill: ${primary} !important; }
          `;

          // Add the dark mode style
          darkStyle.id = "dark-theme-override-style";
          document.head.appendChild(darkStyle);
        } else {
          // Light mode styles - only applied when .dark class is NOT present
          const lightStyle = document.createElement("style");
          lightStyle.textContent = `
            :not(.dark) .bg-primary { background-color: ${primary} !important; }
            :not(.dark) .text-primary:not(button):not(.btn):not(button *):not(.btn *):not(svg):not(svg *) { color: ${primary} !important; }
            :not(.dark) .border-primary { border-color: ${primary} !important; }
            :not(.dark) .hover\\:bg-primary:hover { background-color: ${primary} !important; }
            :not(.dark) nav, :not(.dark) nav.bg-primary { background-color: ${primary} !important; }

            /* Additional selectors to ensure elements get the theme */
            :not(.dark) [class*="bg-primary"]:not(button):not(.btn) { background-color: ${primary} !important; }
            :not(.dark) [class*="text-primary"]:not(button):not(.btn):not(button *):not(.btn *):not(svg):not(svg *) { color: ${primary} !important; }
            :not(.dark) [class*="border-primary"] { border-color: ${primary} !important; }

            /* Force buttons with primary class to use the theme color for background only */
            :not(.dark) button.bg-primary, :not(.dark) .btn-primary {
              background-color: ${primary} !important;
              border-color: ${primary} !important;
              color: white !important;
            }

            /* Ensure text inside buttons remains visible */
            :not(.dark) button.bg-primary *, :not(.dark) .btn-primary * {
              color: white !important;
            }

            /* Icon fixes are now handled in icon-fix.css */

            /* Force links with primary class to use the theme color */
            :not(.dark) a.text-primary { color: ${primary} !important; }

            /* Force SVG icons with primary class to use the theme color, but preserve others */
            :not(.dark) svg.text-primary { color: ${primary} !important; fill: ${primary} !important; }
          `;

          // Add the light mode style
          lightStyle.id = "light-theme-override-style";
          document.head.appendChild(lightStyle);
        }
      }

      // Broadcast a custom event that other components can listen for
      const themeChangeEvent = new CustomEvent("theme-applied", {
        detail: {
          primary,
          secondary,
          primaryHsl,
          secondaryHsl,
          isDarkMode: document.documentElement.classList.contains("dark"),
        },
      });

      window.dispatchEvent(themeChangeEvent);
    } catch (error) {
      console.error("Error applying theme to document:", error);

      // Fallback to dynamic import if the local function fails
      import("@/lib/utils/color-utils")
        .then(({ hexToHSL }) => {
          // Convert hex to HSL for CSS variables
          const primaryHsl = hexToHSL(primary);
          const secondaryHsl = hexToHSL(secondary);

          // Apply to document root
          document.documentElement.style.setProperty("--primary", primaryHsl);
          document.documentElement.style.setProperty(
            "--secondary",
            secondaryHsl
          );
          document.documentElement.style.setProperty(
            "--primary-color",
            primary
          );
          document.documentElement.style.setProperty(
            "--secondary-color",
            secondary
          );

          // Force a CSS refresh
          document.documentElement.classList.add("theme-refreshing");
          setTimeout(() => {
            document.documentElement.classList.remove("theme-refreshing");
          }, 100);
        })
        .catch((importError) => {
          console.error("Error importing color utilities:", importError);
        });
    }
  };

  // Function to manually apply theme (for settings page)
  const applyTheme = (primary: string, secondary: string) => {
    setPrimaryColor(primary);
    setSecondaryColor(secondary);
    applyThemeToDocument(primary, secondary);

    // Also update the database if we have a current school
    if (currentSchool?.id) {
      // Use an async IIFE to handle the database update
      (async () => {
        try {
          // Import supabase client
          const { supabase } = await import("@/lib/supabase");

          // Update the school_branding table
          const { error } = await supabase.from("school_branding").upsert(
            {
              school_id: currentSchool.id,
              primary_color: primary,
              secondary_color: secondary,
              logo_url: logoUrl,
              updated_at: new Date().toISOString(),
            },
            {
              onConflict: "school_id",
            }
          );

          if (error) {
            console.error("Error updating branding in database:", error);
          }
        } catch (error) {
          console.error("Error in database update:", error);
        }
      })();
    }

    // Return a promise that resolves when the theme has been applied
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        resolve();
      }, 200); // Wait for the theme to be applied
    });
  };

  // Helper function to convert hex color to HSL string
  const hexToHSL = (hex: string): string => {
    // Remove the # if present
    hex = hex.replace(/^#/, "");

    // Parse the hex values
    let r = 0,
      g = 0,
      b = 0;
    if (hex.length === 3) {
      r = parseInt(hex[0] + hex[0], 16);
      g = parseInt(hex[1] + hex[1], 16);
      b = parseInt(hex[2] + hex[2], 16);
    } else if (hex.length === 6) {
      r = parseInt(hex.substring(0, 2), 16);
      g = parseInt(hex.substring(2, 4), 16);
      b = parseInt(hex.substring(4, 6), 16);
    } else {
      // Default to black if invalid hex
      return "0 0% 0%";
    }

    // Convert RGB to HSL
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0,
      s = 0,
      l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case r:
          h = (g - b) / d + (g < b ? 6 : 0);
          break;
        case g:
          h = (b - r) / d + 2;
          break;
        case b:
          h = (r - g) / d + 4;
          break;
      }

      h /= 6;
    }

    // Convert to degrees, percentage, percentage
    h = Math.round(h * 360);
    s = Math.round(s * 100);
    l = Math.round(l * 100);

    return `${h} ${s}% ${l}%`;
  };

  // Function to set the logo URL and update the navbar
  const updateLogoUrl = (url: string | null) => {
    setLogoUrl(url);

    // Update the logo in the navbar if it exists
    try {
      const navbarLogo = document.querySelector(
        ".navbar-school-logo"
      ) as HTMLImageElement;
      if (navbarLogo && url) {
        navbarLogo.src = url;
        navbarLogo.style.display = "block";
      }
    } catch (error) {
      console.error("Error updating navbar logo:", error);
    }
  };

  return (
    <SchoolThemeContext.Provider
      value={{
        primaryColor,
        secondaryColor,
        logoUrl,
        applyTheme,
        setLogoUrl: updateLogoUrl,
      }}
    >
      {children}
    </SchoolThemeContext.Provider>
  );
}
