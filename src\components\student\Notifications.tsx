import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  Card<PERSON><PERSON>le,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Bell,
  Check,
  Calendar,
  X,
  Clock,
  AlertCircle,
  Trash2,
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { motion, AnimatePresence } from "framer-motion";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";

type NotificationType = {
  id: string;
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  isDismissing?: boolean; // Flag to track dismissal animation state
  type:
    | "attendance"
    | "absence"
    | "late"
    | "excused"
    | "system"
    | "attendance_alert"
    | "system_alert"
    | "distance_alert";
  metadata?: {
    attendance_id?: string;
    room_id?: string;
    status?: string;
    verification_method?: string;
    notification_type?: string;
    teacher_name?: string;
    teacher_id?: string;
    reminder_time?: string;
    target_type?: string;
    [key: string]: any;
  };
};

const isValidNotificationType = (
  type: string
): type is NotificationType["type"] => {
  return [
    "attendance",
    "absence",
    "late",
    "excused",
    "system",
    "attendance_alert",
    "system_alert",
    "distance_alert",
  ].includes(type);
};

export default function StudentNotifications() {
  const [notifications, setNotifications] = useState<NotificationType[]>([]);
  const [loading, setLoading] = useState(true);
  const { profile } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();

  // Define fetchNotifications outside useEffect so it can be called from other places
  const fetchNotifications = async () => {
    if (!profile) return;

    setLoading(true);

    try {
      // Fetch notifications from the new notifications table with UUID cast
      const { data, error } = await supabase
        .from("notifications")
        .select("*")
        .eq("student_id", profile.id as string)
        .order("timestamp", { ascending: false });

      if (error) throw error;

      // Check for invalid notification types
      const invalidTypes = data
        .filter((notification) => !isValidNotificationType(notification.type))
        .map((n) => n.type);

      // Transform and validate the data
      const formattedNotifications = data
        .filter((notification) => isValidNotificationType(notification.type))
        .map((notification) => ({
          id: notification.id,
          title: notification.title,
          message: notification.message,
          timestamp: new Date(notification.timestamp),
          read: notification.read,
          type: notification.type as NotificationType["type"],
          metadata: notification.metadata as NotificationType["metadata"],
        }));
      setNotifications(formattedNotifications);
    } catch (err) {
      console.error("Error fetching notifications:", err);
      setNotifications([]);
      toast({
        title: t("notifications.error"),
        description: t("notifications.failedToLoad"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!profile) return;

    fetchNotifications();

    // Set up real-time subscription for new notifications
    const channel = supabase
      .channel("student-notifications")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "notifications",
          filter: `student_id=eq.${profile.id}::uuid`,
        },
        (payload) => {
          if (
            payload.eventType === "INSERT" &&
            isValidNotificationType(payload.new.type)
          ) {
            const newNotification = payload.new;
            setNotifications((prev) => [
              {
                id: newNotification.id,
                title: newNotification.title,
                message: newNotification.message,
                timestamp: new Date(newNotification.timestamp),
                read: newNotification.read,
                type: newNotification.type as NotificationType["type"],
                metadata:
                  newNotification.metadata as NotificationType["metadata"],
              },
              ...prev,
            ]);
          } else if (
            payload.eventType === "UPDATE" &&
            isValidNotificationType(payload.new.type)
          ) {
            setNotifications((prev) =>
              prev.filter((n) => n.id !== payload.new.id)
            );
          } else if (
            payload.eventType === "DELETE" &&
            isValidNotificationType(payload.old.type)
          ) {
            setNotifications((prev) =>
              prev.filter((n) => n.id !== payload.old.id)
            );
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [profile]);

  const markAsRead = async (id: string) => {
    try {
      // First update the UI to trigger the exit animation
      // We'll mark it as being dismissed in the UI first
      setNotifications((prev) =>
        prev.map((n) => (n.id === id ? { ...n, isDismissing: true } : n))
      );

      // Wait a short time for the animation to start before removing from UI
      setTimeout(() => {
        // Remove from UI to trigger the exit animation
        setNotifications((prev) => prev.filter((n) => n.id !== id));
      }, 50);

      // Delete from database after animation has started
      setTimeout(async () => {
        // Delete the notification from the database
        const { error } = await supabase
          .from("notifications")
          .delete()
          .eq("id", id);

        if (error) {
          console.error("Error deleting notification:", error);
          throw error;
        }

        toast({
          title: t("notifications.dismissed"),
          description: t("notifications.deleted"),
          variant: "default",
        });
      }, 300); // Wait for animation to be well underway
    } catch (err) {
      console.error("Error deleting notification:", err);
      toast({
        title: t("notifications.error"),
        description: t("notifications.failedToDelete"),
        variant: "destructive",
      });
    }
  };

  const clearAllNotifications = async () => {
    if (!profile) return;

    try {
      // First mark all notifications as dismissing to trigger their animations
      // We'll do this in a staggered fashion for a nice visual effect
      const notificationsCopy = [...notifications];

      // Mark each notification as dismissing with a slight delay between each
      notificationsCopy.forEach((notification, index) => {
        setTimeout(() => {
          setNotifications((prev) =>
            prev.map((n) =>
              n.id === notification.id ? { ...n, isDismissing: true } : n
            )
          );

          // After a short delay, remove this notification from the UI
          setTimeout(() => {
            setNotifications((prev) =>
              prev.filter((n) => n.id !== notification.id)
            );
          }, 100);
        }, index * 50); // Stagger the dismissal by 50ms per notification
      });

      // Calculate total animation time based on number of notifications
      const totalAnimationTime = notificationsCopy.length * 50 + 400;

      // Wait for all animations to complete before deleting from database
      setTimeout(async () => {
        // Delete all notifications for this student from the database
        const { error } = await supabase
          .from("notifications")
          .delete()
          .eq("student_id", profile.id);

        if (error) {
          console.error("Error deleting notifications:", error);
          throw error;
        }

        toast({
          title: t("notifications.allClear"),
          description: t("notifications.allDeleted"),
          variant: "default",
        });
      }, totalAnimationTime); // Wait for all animations to complete
    } catch (err) {
      console.error("Error deleting all notifications:", err);
      toast({
        title: t("notifications.error"),
        description: t("notifications.failedToDeleteAll"),
        variant: "destructive",
      });
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "attendance":
      case "attendance_alert":
        return <Calendar className="w-3 h-3 sm:w-4 sm:h-4 text-green-500" />;
      case "absence":
        return <X className="w-3 h-3 sm:w-4 sm:h-4 text-red-500" />;
      case "late":
        return <Clock className="w-3 h-3 sm:w-4 sm:h-4 text-amber-500" />;
      case "excused":
        return <AlertCircle className="w-3 h-3 sm:w-4 sm:h-4 text-blue-500" />;
      case "system":
      case "system_alert":
        return <Bell className="w-3 h-3 sm:w-4 sm:h-4 text-blue-500" />;
      case "distance_alert":
        return <AlertCircle className="w-3 h-3 sm:w-4 sm:h-4 text-amber-500" />;
      default:
        return <Bell className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "attendance":
      case "attendance_alert":
        return "bg-green-100 text-green-800";
      case "absence":
        return "bg-red-100 text-red-800";
      case "late":
        return "bg-amber-100 text-amber-800";
      case "excused":
        return "bg-blue-100 text-blue-800";
      case "system":
      case "system_alert":
        return "bg-blue-100 text-blue-800";
      case "distance_alert":
        return "bg-amber-100 text-amber-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatTimeAgo = (date: Date) => {
    const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);

    let interval = seconds / 31536000;
    if (interval > 1)
      return t("notifications.timeAgo.years", { count: Math.floor(interval) });

    interval = seconds / 2592000;
    if (interval > 1)
      return t("notifications.timeAgo.months", { count: Math.floor(interval) });

    interval = seconds / 86400;
    if (interval > 1)
      return t("notifications.timeAgo.days", { count: Math.floor(interval) });

    interval = seconds / 3600;
    if (interval > 1)
      return t("notifications.timeAgo.hours", { count: Math.floor(interval) });

    interval = seconds / 60;
    if (interval > 1)
      return t("notifications.timeAgo.minutes", {
        count: Math.floor(interval),
      });

    return t("notifications.timeAgo.seconds", { count: Math.floor(seconds) });
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader className="px-3 sm:px-6 py-3 sm:py-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0">
          <div>
            <CardTitle className="text-lg sm:text-xl">
              {t("notifications.title")}
            </CardTitle>
            <CardDescription className="text-xs sm:text-sm">
              {t("notifications.description")}
            </CardDescription>
          </div>
          <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
            {notifications.some((n) => !n.read) && (
              <Badge variant="secondary" className="animate-pulse text-xs">
                {notifications.filter((n) => !n.read).length}{" "}
                {t("notifications.new")}
              </Badge>
            )}

            {notifications.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllNotifications}
                className="flex items-center gap-1 text-destructive hover:text-destructive hover:bg-destructive/10 text-xs h-7 sm:h-8"
              >
                <Trash2 size={12} className="sm:h-4 sm:w-4" />
                {t("notifications.clearAll")}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="px-3 sm:px-6 py-3 sm:py-4">
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : notifications.length > 0 ? (
          <div className="space-y-4">
            <AnimatePresence mode="sync" initial={false}>
              {notifications.map((notification) => (
                <motion.div
                  key={notification.id}
                  layout
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{
                    opacity: 0,
                    scale: 0,
                    x: 50,
                    y: -20,
                    rotate: 5,
                    transition: {
                      duration: 0.4,
                      ease: [0.32, 0.72, 0, 1], // Custom easing for a more natural feel
                    },
                  }}
                  whileHover={{
                    scale: 1.02,
                    transition: { duration: 0.2 },
                  }}
                  className={`p-3 sm:p-4 border rounded-lg transition-all ${
                    notification.isDismissing
                      ? "bg-green-50 border-green-200"
                      : notification.read
                      ? "bg-white"
                      : "bg-blue-50 border-blue-200"
                  }`}
                >
                  <div className="flex flex-col sm:flex-row justify-between items-start gap-3 sm:gap-0">
                    <div className="flex items-start sm:items-center gap-2 sm:gap-3 w-full">
                      <div
                        className={`p-1.5 sm:p-2 rounded-full flex-shrink-0 ${getTypeColor(
                          notification.type
                        )}`}
                      >
                        {getTypeIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm sm:text-base">
                          {notification.title}
                          {!notification.read && (
                            <span className="inline-block w-2 h-2 bg-blue-500 rounded-full ml-2"></span>
                          )}
                        </h4>
                        <p className="text-xs sm:text-sm text-muted-foreground mt-0.5 sm:mt-1 break-words">
                          {notification.message}
                        </p>
                        <p className="text-[10px] sm:text-xs text-muted-foreground mt-0.5 sm:mt-1">
                          {formatTimeAgo(notification.timestamp)}
                        </p>
                      </div>
                    </div>

                    {!notification.read && (
                      <Button
                        variant={
                          notification.isDismissing ? "default" : "ghost"
                        }
                        size="sm"
                        onClick={() => markAsRead(notification.id)}
                        disabled={notification.isDismissing}
                        className={`shrink-0 transition-all h-7 sm:h-8 text-xs sm:text-sm mt-2 sm:mt-0 ${
                          notification.isDismissing
                            ? "bg-green-500 text-white"
                            : "hover:bg-blue-100 hover:text-blue-700"
                        }`}
                      >
                        <Check
                          size={14}
                          className={`mr-1 ${
                            notification.isDismissing ? "animate-ping" : ""
                          }`}
                        />
                        {notification.isDismissing
                          ? t("notifications.dismissing")
                          : t("notifications.dismiss")}
                      </Button>
                    )}
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="py-6 sm:py-8 text-center text-muted-foreground"
          >
            <Bell
              size={30}
              className="mx-auto opacity-20 mb-2 sm:h-10 sm:w-10"
            />
            <p className="text-base sm:text-lg font-medium mb-1">
              {t("notifications.noNotificationsYet")}
            </p>
            <p className="text-xs sm:text-sm">
              {t("notifications.emptyStateMessage")}
            </p>
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
}
