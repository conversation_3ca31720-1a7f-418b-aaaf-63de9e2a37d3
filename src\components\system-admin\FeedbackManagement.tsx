import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { supabase } from "@/lib/supabase";
import {
  getAllFeedbackSubmissions,
  updateFeedbackStatus,
  deleteFeedback,
  FeedbackSubmission,
} from "@/lib/api/feedback";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MessageSquare,
  MoreVertical,
  RefreshCw,
  Search,
  Trash2,
  CheckCircle,
  ArchiveIcon,
  Eye,
  Mail,
  Phone,
  User,
  Calendar,
  School,
} from "lucide-react";
import { format } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";

export default function FeedbackManagement() {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [feedback, setFeedback] = useState<FeedbackSubmission[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFeedback, setSelectedFeedback] =
    useState<FeedbackSubmission | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [processingAction, setProcessingAction] = useState(false);
  const [schools, setSchools] = useState<Record<string, string>>({});

  // Fetch feedback submissions
  const fetchFeedback = async () => {
    setLoading(true);
    try {
      const data = await getAllFeedbackSubmissions();
      setFeedback(data);
    } catch (error) {
      console.error("Error fetching feedback:", error);
      toast.error(t("Error"), {
        description: t("Failed to load feedback submissions."),
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch school names
  const fetchSchoolNames = async () => {
    try {
      const { data, error } = await supabase.from("schools").select("id, name");

      if (error) {
        console.error("Error fetching schools:", error);
        return;
      }

      const schoolMap: Record<string, string> = {};
      data.forEach((school) => {
        schoolMap[school.id] = school.name;
      });

      setSchools(schoolMap);
    } catch (error) {
      console.error("Error fetching schools:", error);
    }
  };

  // Load feedback on component mount and set up real-time subscription
  useEffect(() => {
    fetchFeedback();
    fetchSchoolNames();

    // Set up real-time subscription to feedback_submissions table
    const subscription = supabase
      .channel("feedback_changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "feedback_submissions",
        },
        (payload) => {
          console.log("Feedback change received:", payload);

          // Handle different types of changes
          if (payload.eventType === "INSERT") {
            // Add new feedback to the list
            setFeedback((prev) => [payload.new as FeedbackSubmission, ...prev]);

            // Show toast notification for new feedback
            toast.success(t("New Feedback"), {
              description: t("A new feedback has been submitted."),
            });
          } else if (payload.eventType === "UPDATE") {
            // Update existing feedback in the list
            setFeedback((prev) =>
              prev.map((item) =>
                item.id === payload.new.id
                  ? (payload.new as FeedbackSubmission)
                  : item
              )
            );
          } else if (payload.eventType === "DELETE") {
            // Remove deleted feedback from the list
            setFeedback((prev) =>
              prev.filter((item) => item.id !== payload.old.id)
            );
          }
        }
      )
      .subscribe();

    // Clean up subscription when component unmounts
    return () => {
      subscription.unsubscribe();
    };
  }, [t, toast]);

  // Filter feedback based on search query
  const filteredFeedback = feedback.filter((item) => {
    const searchString = searchQuery.toLowerCase();
    return (
      item.name?.toLowerCase().includes(searchString) ||
      false ||
      item.email?.toLowerCase().includes(searchString) ||
      false ||
      item.message.toLowerCase().includes(searchString) ||
      item.user_role?.toLowerCase().includes(searchString) ||
      false
    );
  });

  // Group feedback by status
  const unreadFeedback = filteredFeedback.filter(
    (item) => item.status === "unread"
  );
  const readFeedback = filteredFeedback.filter(
    (item) => item.status === "read"
  );
  const archivedFeedback = filteredFeedback.filter(
    (item) => item.status === "archived"
  );

  // Handle marking feedback as read
  const handleMarkAsRead = async (id: string) => {
    setProcessingAction(true);
    try {
      const success = await updateFeedbackStatus(id, "read");
      if (success) {
        setFeedback((prev) =>
          prev.map((item) =>
            item.id === id ? { ...item, status: "read" } : item
          )
        );
        toast.success(t("Success"), {
          description: t("Feedback marked as read."),
        });
      } else {
        toast.error(t("Error"), {
          description: t("Failed to update feedback status."),
        });
      }
    } catch (error) {
      console.error("Error updating feedback status:", error);
      toast.error(t("Error"), {
        description: t("An unexpected error occurred."),
      });
    } finally {
      setProcessingAction(false);
    }
  };

  // Handle archiving feedback
  const handleArchive = async (id: string) => {
    setProcessingAction(true);
    try {
      const success = await updateFeedbackStatus(id, "archived");
      if (success) {
        setFeedback((prev) =>
          prev.map((item) =>
            item.id === id ? { ...item, status: "archived" } : item
          )
        );
        toast.success(t("Success"), {
          description: t("Feedback archived."),
        });
      } else {
        toast.error(t("Error"), {
          description: t("Failed to archive feedback."),
        });
      }
    } catch (error) {
      console.error("Error archiving feedback:", error);
      toast.error(t("Error"), {
        description: t("An unexpected error occurred."),
      });
    } finally {
      setProcessingAction(false);
    }
  };

  // Handle deleting feedback
  const handleDelete = async () => {
    if (!selectedFeedback) return;

    setProcessingAction(true);
    try {
      const success = await deleteFeedback(selectedFeedback.id);
      if (success) {
        setFeedback((prev) =>
          prev.filter((item) => item.id !== selectedFeedback.id)
        );
        setDeleteDialogOpen(false);
        setSelectedFeedback(null);
        toast.success(t("Success"), {
          description: t("Feedback deleted successfully."),
        });
      } else {
        toast.error(t("Error"), {
          description: t("Failed to delete feedback."),
        });
      }
    } catch (error) {
      console.error("Error deleting feedback:", error);
      toast.error(t("Error"), {
        description: t("An unexpected error occurred."),
      });
    } finally {
      setProcessingAction(false);
    }
  };

  // Render feedback status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "unread":
        return (
          <Badge variant="default" className="bg-blue-500">
            {t("Unread")}
          </Badge>
        );
      case "read":
        return (
          <Badge
            variant="outline"
            className="bg-green-100 text-green-800 border-green-200"
          >
            {t("Read")}
          </Badge>
        );
      case "archived":
        return (
          <Badge
            variant="outline"
            className="bg-gray-100 text-gray-800 border-gray-200"
          >
            {t("Archived")}
          </Badge>
        );
      default:
        return null;
    }
  };

  // Render feedback table
  const renderFeedbackTable = (items: FeedbackSubmission[]) => {
    if (loading) {
      return (
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center space-x-4">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-[250px]" />
                <Skeleton className="h-4 w-[200px]" />
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (items.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">{t("No feedback found")}</h3>
          <p className="text-sm text-muted-foreground mt-2">
            {searchQuery
              ? t("No feedback matches your search criteria.")
              : t("There are no feedback submissions in this category.")}
          </p>
        </div>
      );
    }

    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t("From")}</TableHead>
            <TableHead>{t("Message")}</TableHead>
            <TableHead>{t("Date")}</TableHead>
            <TableHead>{t("Status")}</TableHead>
            <TableHead className="text-right">{t("Actions")}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {items.map((item) => (
            <TableRow key={item.id}>
              <TableCell className="font-medium">
                <div className="flex flex-col">
                  <span>{item.name || t("Anonymous")}</span>
                  {item.user_role && (
                    <span className="text-xs text-muted-foreground">
                      {item.user_role}
                    </span>
                  )}
                </div>
              </TableCell>
              <TableCell className="max-w-xs truncate">
                {item.message}
              </TableCell>
              <TableCell>
                {format(new Date(item.created_at), "MMM d, yyyy")}
              </TableCell>
              <TableCell>{renderStatusBadge(item.status)}</TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreVertical className="h-4 w-4" />
                      <span className="sr-only">{t("Actions")}</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>{t("Actions")}</DropdownMenuLabel>
                    <DropdownMenuItem
                      onClick={() => {
                        setSelectedFeedback(item);
                        setViewDialogOpen(true);
                        if (item.status === "unread") {
                          handleMarkAsRead(item.id);
                        }
                      }}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      {t("View Details")}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {item.status === "unread" && (
                      <DropdownMenuItem
                        onClick={() => handleMarkAsRead(item.id)}
                      >
                        <CheckCircle className="mr-2 h-4 w-4" />
                        {t("Mark as Read")}
                      </DropdownMenuItem>
                    )}
                    {item.status !== "archived" && (
                      <DropdownMenuItem onClick={() => handleArchive(item.id)}>
                        <ArchiveIcon className="mr-2 h-4 w-4" />
                        {t("Archive")}
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => {
                        setSelectedFeedback(item);
                        setDeleteDialogOpen(true);
                      }}
                      className="text-red-600"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      {t("Delete")}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            {t("Feedback Management")}
          </CardTitle>
          <CardDescription>
            {t("View and manage feedback submissions from users.")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-between">
              <div className="relative w-full max-w-sm">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder={t("Search feedback...")}
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button
                variant="outline"
                size="icon"
                onClick={fetchFeedback}
                disabled={loading}
              >
                <RefreshCw
                  className={cn("h-4 w-4", loading && "animate-spin")}
                />
                <span className="sr-only">{t("Refresh")}</span>
              </Button>
            </div>

            <Tabs defaultValue="unread" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="unread" className="flex items-center gap-2">
                  <Badge variant="secondary" className="ml-1">
                    {unreadFeedback.length}
                  </Badge>
                  {t("Unread")}
                </TabsTrigger>
                <TabsTrigger value="read" className="flex items-center gap-2">
                  <Badge variant="secondary" className="ml-1">
                    {readFeedback.length}
                  </Badge>
                  {t("Read")}
                </TabsTrigger>
                <TabsTrigger
                  value="archived"
                  className="flex items-center gap-2"
                >
                  <Badge variant="secondary" className="ml-1">
                    {archivedFeedback.length}
                  </Badge>
                  {t("Archived")}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="unread" className="mt-4">
                {renderFeedbackTable(unreadFeedback)}
              </TabsContent>

              <TabsContent value="read" className="mt-4">
                {renderFeedbackTable(readFeedback)}
              </TabsContent>

              <TabsContent value="archived" className="mt-4">
                {renderFeedbackTable(archivedFeedback)}
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* View Feedback Dialog */}
      <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{t("Feedback Details")}</DialogTitle>
            <DialogDescription>
              {selectedFeedback?.created_at &&
                format(
                  new Date(selectedFeedback.created_at),
                  "MMMM d, yyyy 'at' h:mm a"
                )}
            </DialogDescription>
          </DialogHeader>

          {selectedFeedback && (
            <div className="space-y-4">
              <div className="grid grid-cols-[20px_1fr] items-start gap-2">
                <User className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">
                    {selectedFeedback.name || t("Anonymous")}
                  </p>
                  {selectedFeedback.user_role && (
                    <p className="text-sm text-muted-foreground">
                      {selectedFeedback.user_role}
                    </p>
                  )}
                </div>
              </div>

              {selectedFeedback.email && (
                <div className="grid grid-cols-[20px_1fr] items-start gap-2">
                  <Mail className="h-5 w-5 text-muted-foreground" />
                  <p>{selectedFeedback.email}</p>
                </div>
              )}

              {selectedFeedback.phone && (
                <div className="grid grid-cols-[20px_1fr] items-start gap-2">
                  <Phone className="h-5 w-5 text-muted-foreground" />
                  <p>{selectedFeedback.phone}</p>
                </div>
              )}

              {selectedFeedback.school_id && (
                <div className="grid grid-cols-[20px_1fr] items-start gap-2">
                  <School className="h-5 w-5 text-muted-foreground" />
                  <p>
                    {schools[selectedFeedback.school_id] ||
                      selectedFeedback.school_id}
                  </p>
                </div>
              )}

              <div className="grid grid-cols-[20px_1fr] items-start gap-2">
                <Calendar className="h-5 w-5 text-muted-foreground" />
                <p>
                  {format(
                    new Date(selectedFeedback.created_at),
                    "MMMM d, yyyy 'at' h:mm a"
                  )}
                </p>
              </div>

              <div className="rounded-md border p-4 mt-4">
                <p className="whitespace-pre-wrap">
                  {selectedFeedback.message}
                </p>
              </div>
            </div>
          )}

          <DialogFooter className="flex justify-between">
            <div className="flex gap-2">
              {selectedFeedback?.status !== "archived" && (
                <Button
                  variant="outline"
                  onClick={() => {
                    handleArchive(selectedFeedback?.id || "");
                    setViewDialogOpen(false);
                  }}
                  disabled={processingAction}
                >
                  <ArchiveIcon className="mr-2 h-4 w-4" />
                  {t("Archive")}
                </Button>
              )}
              <Button
                variant="destructive"
                onClick={() => {
                  setViewDialogOpen(false);
                  setDeleteDialogOpen(true);
                }}
                disabled={processingAction}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                {t("Delete")}
              </Button>
            </div>
            <Button variant="outline" onClick={() => setViewDialogOpen(false)}>
              {t("Close")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{t("Delete Feedback")}</DialogTitle>
            <DialogDescription>
              {t(
                "Are you sure you want to delete this feedback? This action cannot be undone."
              )}
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={processingAction}
            >
              {t("Cancel")}
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={processingAction}
            >
              {processingAction ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  {t("Deleting...")}
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  {t("Delete")}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
