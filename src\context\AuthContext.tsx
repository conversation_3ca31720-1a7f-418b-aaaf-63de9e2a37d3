import {
  createContext,
  useContext,
  useEffect,
  useState,
  useRef,
  useCallback,
} from "react";
import { supabase } from "@/lib/supabase";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import type { Session, User as SupabaseUser } from "@supabase/supabase-js";
import { User } from "@/lib/types";
import { createBlockLocationsTable } from "@/lib/migrations.new";
import { createParentContactsTable } from "@/lib/migrations/parent-contacts-migration";
import { migrateAdminProfiles } from "@/lib/migrations/admin-profiles-migration";
import { createSchoolSettingsTables } from "@/lib/migrations/school-settings-migration";
import { checkAndTriggerCleanup } from "@/lib/services/database-cleanup-service";
import { useTabVisibility } from "@/hooks/useTabVisibility";

type AuthContextType = {
  user: SupabaseUser | null;
  profile: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signInWithBiometrics: (email: string) => Promise<void>;
  signUp: (
    email: string,
    password: string,
    userData: Partial<User>
  ) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [profile, setProfile] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const navigate = useNavigate();

  // Tab visibility to prevent unnecessary operations when tab is not active
  const isTabVisible = useTabVisibility();

  // Refs to prevent duplicate operations
  const initializationRef = useRef(false);
  const profileFetchRef = useRef<string | null>(null);
  const authListenerRef = useRef<any>(null);

  // Function to fetch profile data with caching
  const fetchProfile = useCallback(
    async (userId: string) => {
      // Prevent duplicate profile fetches for the same user
      if (profileFetchRef.current === userId) {
        return profile;
      }

      profileFetchRef.current = userId;

      try {
        // Query the profiles table for the user's profile
        const { data: profileData, error } = await supabase
          .from("profiles")
          .select("*")
          .eq("user_id", userId)
          .single();

        if (error) {
          if (error.code === "PGRST116" || error.code === "406") {
            // No profile found for this user, creating from metadata
            // Profile not found, create it from metadata
            try {
              const { data: userData, error: userError } =
                await supabase.auth.getUser();

              if (userError) {
                console.error("Error fetching user data:", userError);
                // Try to get user info from session instead
                const { data: sessionData } = await supabase.auth.getSession();
                const user = sessionData?.session?.user;

                if (!user) {
                  console.error("Could not get user data from session either");
                  return null;
                }

                const metadata = user.user_metadata || {};

                // Create a profile record in the database
                const { data: newProfile, error: insertError } = await supabase
                  .from("profiles")
                  .insert({
                    id: userId, // Use the user ID as the profile ID
                    user_id: userId,
                    name: metadata.name || "User",
                    email: user.email || "",
                    role:
                      (metadata.role as "student" | "teacher" | "admin") ||
                      "student",
                  })
                  .select("*")
                  .single();

                if (insertError) {
                  console.error("Error creating profile:", insertError);
                  return null;
                }

                // Continue with the existing code...
                // Transform to match our app's User type
                const userProfile: User = {
                  id: newProfile.id,
                  user_id: newProfile.user_id,
                  name: newProfile.name,
                  email: newProfile.email,
                  role: newProfile.role as "student" | "teacher" | "admin",
                  photoUrl: newProfile.photo_url || undefined,
                  studentId: newProfile.student_id || undefined,
                  teacherId: newProfile.teacher_id || undefined,
                  adminId: newProfile.admin_id || undefined,
                  department: newProfile.department || undefined,
                  position: newProfile.position || undefined,
                  subject: newProfile.subject || undefined,
                  course: newProfile.course || undefined,
                  biometricRegistered: newProfile.biometric_registered || false,
                  blockName: newProfile.block_name || undefined,
                  roomNumber: newProfile.room_number || undefined,
                  pin: newProfile.pin || undefined,
                  school: newProfile.school || undefined,
                  profileCompleted: newProfile.profile_completed || false,
                  created_at: newProfile.created_at || undefined,
                  updated_at: newProfile.updated_at || undefined,
                };

                setProfile(userProfile);
                return userProfile;
              }

              if (userData && userData.user) {
                const metadata = userData.user.user_metadata || {};

                // Create a profile record in the database
                const { data: newProfile, error: insertError } = await supabase
                  .from("profiles")
                  .insert({
                    id: userId, // Use the user ID as the profile ID
                    user_id: userId,
                    name: metadata.name || "User",
                    email: userData.user.email || "",
                    role:
                      (metadata.role as "student" | "teacher" | "admin") ||
                      "student",
                  })
                  .select("*")
                  .single();

                if (insertError) {
                  console.error("Error creating profile:", insertError);
                  return null;
                }

                // Transform to match our app's User type
                const userProfile: User = {
                  id: newProfile.id,
                  user_id: newProfile.user_id,
                  name: newProfile.name,
                  email: newProfile.email,
                  role: newProfile.role as "student" | "teacher" | "admin",
                  photoUrl: newProfile.photo_url || undefined,
                  studentId: newProfile.student_id || undefined,
                  teacherId: newProfile.teacher_id || undefined,
                  adminId: newProfile.admin_id || undefined,
                  department: newProfile.department || undefined,
                  position: newProfile.position || undefined,
                  subject: newProfile.subject || undefined,
                  course: newProfile.course || undefined,
                  biometricRegistered: newProfile.biometric_registered || false,
                  blockName: newProfile.block_name || undefined,
                  roomNumber: newProfile.room_number || undefined,
                  pin: newProfile.pin || undefined,
                  school: newProfile.school || undefined,
                  profileCompleted: newProfile.profile_completed || false,
                  created_at: newProfile.created_at || undefined,
                  updated_at: newProfile.updated_at || undefined,
                };

                setProfile(userProfile);
                return userProfile;
              }
            } catch (innerError) {
              console.error("Error in profile creation:", innerError);
              return null;
            }
          } else {
            console.error("Error fetching profile:", error);
          }
          return null;
        } else if (profileData) {
          // Transform Supabase data to match our app's User type
          const userProfile: User = {
            id: profileData.id,
            user_id: profileData.user_id,
            role: profileData.role as "student" | "teacher" | "admin",
            name: profileData.name,
            email: profileData.email,
            photoUrl: profileData.photo_url || undefined,
            studentId: profileData.student_id || undefined,
            teacherId: profileData.teacher_id || undefined,
            adminId: profileData.admin_id || undefined,
            department: profileData.department || undefined,
            position: profileData.position || undefined,
            subject: profileData.subject || undefined,
            course: profileData.course || undefined,
            biometricRegistered: profileData.biometric_registered || false,
            block_id: profileData.block_id || undefined, // ✅ Added missing block_id
            room_id: profileData.room_id || undefined, // ✅ Added missing room_id
            blockName: profileData.block_name || undefined,
            roomNumber: profileData.room_number || undefined,
            pin: profileData.pin || undefined,
            school_id: profileData.school_id || undefined,
            school: profileData.school || undefined,
            accessLevel: profileData.access_level || undefined,
            profileCompleted: profileData.profile_completed || false,
            created_at: profileData.created_at || undefined,
            updated_at: profileData.updated_at || undefined,
            preferred_language: profileData.preferred_language || "en",
          };

          setProfile(userProfile);
          return userProfile;
        }
        return null;
      } catch (error) {
        console.error("Error fetching profile data:", error);
        return null;
      }
    },
    [profile]
  );

  // Function to handle redirects based on user role
  const handleRedirectByRole = (userProfile: User | null) => {
    if (!userProfile) {
      return;
    }

    // First check if profile is marked as completed
    if (userProfile.profile_completed === true) {
      // Profile is completed, redirect to appropriate dashboard
      if (userProfile.role === "student") {
        navigate("/student");
      } else if (userProfile.role === "teacher") {
        navigate("/teacher");
      } else if (userProfile.role === "admin") {
        // If user is a system admin (access_level 3), redirect to system admin dashboard
        if (userProfile.accessLevel === 3) {
          navigate("/system-admin");
        } else {
          navigate("/admin");
        }
      }
      return;
    }

    // Profile is not completed, check individual fields and redirect to setup
    if (userProfile.role === "student") {
      // If student profile isn't complete, redirect to profile setup
      if (
        !userProfile.course ||
        !userProfile.block_id ||
        !userProfile.room_id ||
        !userProfile.pin ||
        !userProfile.school_id
      ) {
        navigate("/student?setup=true");
      } else {
        navigate("/student");
      }
    } else if (userProfile.role === "teacher") {
      // If teacher profile isn't complete, redirect to profile setup
      if (
        !userProfile.department ||
        !userProfile.position ||
        !userProfile.subject ||
        !userProfile.school_id
      ) {
        navigate("/teacher?setup=true");
      } else {
        navigate("/teacher");
      }
    } else if (userProfile.role === "admin") {
      // If admin profile isn't complete, redirect to profile setup
      if (!userProfile.position || !userProfile.school) {
        navigate("/admin-profile-setup?setup=true");
      } else {
        // If user is a system admin (access_level 3), redirect to system admin dashboard
        if (userProfile.accessLevel === 3) {
          navigate("/system-admin");
        } else {
          navigate("/admin");
        }
      }
    }
  };

  useEffect(() => {
    // Prevent multiple initializations
    if (initializationRef.current) {
      return;
    }

    initializationRef.current = true;

    const initAuth = async () => {
      setLoading(true);

      try {
        // Initialize database migrations
        try {
          await createBlockLocationsTable();
          await createParentContactsTable();
          await migrateAdminProfiles();
          await createSchoolSettingsTables();
        } catch (migrationError) {
          console.error("Error running migrations:", migrationError);
          // Continue with auth initialization even if migrations fail
        }

        // Set up auth state listener first (only if not already set)
        if (!authListenerRef.current) {
          const {
            data: { subscription },
          } = await supabase.auth.onAuthStateChange((event, currentSession) => {
            setSession(currentSession);
            setUser(currentSession?.user || null);

            if (currentSession?.user) {
              if (event === "SIGNED_IN") {
                // Check if an admin is creating a user - if so, don't redirect
                const isAdminCreatingUser =
                  window.localStorage.getItem("admin_creating_user") === "true";

                if (!isAdminCreatingUser) {
                  // Use setTimeout to avoid potential auth deadlock
                  setTimeout(async () => {
                    const userProfile = await fetchProfile(
                      currentSession.user.id
                    );
                    if (userProfile) {
                      handleRedirectByRole(userProfile);
                    }
                  }, 100);
                }
              }
            } else if (event === "SIGNED_OUT") {
              setProfile(null);
              navigate("/login");
            }
          });

          // Store the subscription reference
          authListenerRef.current = subscription;
        }

        // Check for biometric session first
        const biometricSessionActive = localStorage.getItem('biometric_session_active');
        const biometricUserData = localStorage.getItem('biometric_user_data');

        if (biometricSessionActive === 'true' && biometricUserData) {
          try {
            const userData = JSON.parse(biometricUserData);

            // Create a mock user and session for biometric authentication
            const mockUser = {
              id: userData.user_id,
              email: userData.email,
              user_metadata: {
                biometric_auth: true,
                name: userData.name,
                role: userData.role
              }
            };

            const mockSession = {
              user: mockUser,
              access_token: `biometric_${userData.user_id}_${Date.now()}`,
              refresh_token: `refresh_${userData.user_id}_${Date.now()}`,
              expires_in: 3600,
              token_type: 'bearer'
            };

            setUser(mockUser as any);
            setSession(mockSession as any);

            // Create a profile object from the stored data
            const userProfile: User = {
              id: userData.user_id,
              user_id: userData.user_id,
              name: userData.name,
              email: userData.email,
              role: userData.role,
              biometricRegistered: true,
              profileCompleted: true,
              // Add other required fields with defaults
              photoUrl: undefined,
              studentId: undefined,
              teacherId: undefined,
              adminId: undefined,
              department: undefined,
              position: undefined,
              subject: undefined,
              course: undefined,
              blockName: undefined,
              roomNumber: undefined,
              pin: undefined,
              school: undefined,
              created_at: undefined,
              updated_at: undefined,
            };

            setProfile(userProfile);

            // Redirect to appropriate dashboard
            const currentPath = window.location.pathname;
            if (currentPath === "/login" || currentPath === "/signup" || currentPath === "/") {
              handleRedirectByRole(userProfile);
            }

            return;
          } catch (error) {
            console.error('Error processing biometric session:', error);
            // Clear invalid biometric session data
            localStorage.removeItem('biometric_session_active');
            localStorage.removeItem('biometric_user_data');
          }
        }

        // Then check for existing session
        const {
          data: { session: existingSession },
          error: sessionError,
        } = await supabase.auth.getSession();

        if (sessionError) {
          console.error("Error fetching session:", sessionError);
        } else if (existingSession) {
          setSession(existingSession);
          setUser(existingSession.user);

          // Fetch user profile from profiles table
          const userProfile = await fetchProfile(existingSession.user.id);

          // If we're on the login or signup page but have a valid session, redirect
          const currentPath = window.location.pathname;
          if (
            (currentPath === "/login" ||
              currentPath === "/signup" ||
              currentPath === "/") &&
            userProfile
          ) {
            handleRedirectByRole(userProfile);
          }
        }

        return () => {
          if (authListenerRef.current) {
            authListenerRef.current.unsubscribe();
            authListenerRef.current = null;
          }
        };
      } catch (error) {
        console.error("Error initializing auth:", error);
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, [navigate]);

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);

      // Direct approach first - no retries or special handling
      try {
        // Clear any existing sessions first to avoid conflicts
        await supabase.auth.signOut({ scope: "local" });

        // Try authentication with email and password
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        // If successful, return the data
        if (data?.user) {
          return { data, error: null };
        }

        // If there's an error, throw it to be caught by the outer catch
        if (error) {
          console.error("Authentication error:", error.message);
          throw error;
        }
      } catch (authError: any) {
        // If the error is related to network issues, try the fallback approach
        if (
          authError.message?.includes("Failed to fetch") ||
          authError.message?.includes("Unexpected end of input") ||
          authError.message?.includes("NetworkError")
        ) {
          // Try a different authentication approach as fallback
          const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password,
            options: {
              // Use different options for fallback
              captchaToken: undefined,
              redirectTo: window.location.origin,
            },
          });

          if (data?.user) {
            return { data, error: null };
          }

          if (error) {
            console.error("Fallback authentication error:", error);
            throw error;
          }
        }

        // Re-throw the original error if it's not a network issue
        throw authError;
      }

      // Get the result from the authentication attempts
      const result = await supabase.auth.getSession();
      const session = result.data.session;
      const user = session?.user;

      if (!user) {
        toast({
          title: "Login failed",
          description:
            "Authentication failed. Please check your credentials and try again.",
          variant: "destructive",
        });
        throw new Error("Authentication failed");
      }

      // Fetch profile and redirect
      toast({
        title: "Login successful",
        description: "Welcome back!",
      });

      const userProfile = await fetchProfile(user.id);
      if (userProfile) {
        // If user is a system admin, check if database cleanup is due
        if (userProfile.role === "admin" && userProfile.accessLevel === 3) {
          try {
            // Check if database cleanup is due and trigger it if necessary
            // This runs asynchronously and doesn't block the login process
            checkAndTriggerCleanup().then((cleanupTriggered) => {
              // Database cleanup triggered if needed
            });
          } catch (cleanupError) {
            console.error("Error checking database cleanup:", cleanupError);
            // Don't block login if cleanup check fails
          }
        }

        handleRedirectByRole(userProfile);
      } else {
        // If no profile exists, redirect to login page with error
        toast({
          title: "Profile error",
          description: "Could not find your profile. Please contact support.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("Sign in error:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (
    email: string,
    password: string,
    userData: Partial<User>
  ) => {
    try {
      setLoading(true);

      // Validate invitation code if provided
      if (userData.school_id && userData.invitationCode) {
        // Get school data and invitation code
        const { data: schoolData, error: schoolError } = await supabase
          .from("schools")
          .select("id, invitation_code")
          .eq("id", userData.school_id)
          .single();

        if (schoolError) {
          console.error("Error validating school:", schoolError);
          toast({
            title: "Sign up failed",
            description: "Invalid school selected",
            variant: "destructive",
          });
          throw new Error("Invalid school selected");
        }

        // Check if invitation code exists
        if (!schoolData.invitation_code) {
          console.error("School has no invitation code:", schoolData.id);
          toast({
            title: "Sign up failed",
            description:
              "This school doesn't have an active invitation code. Please contact the school administrator.",
            variant: "destructive",
          });
          throw new Error("No active invitation code");
        }

        // Invitation codes no longer expire, so we don't need to check expiry
        // We're keeping this comment as a placeholder in case we need to reimplement expiry in the future

        // Case-insensitive comparison of invitation codes
        // Trim whitespace and normalize case
        const normalizedInputCode = userData.invitationCode
          .trim()
          .toUpperCase();
        const normalizedStoredCode = schoolData.invitation_code
          .trim()
          .toUpperCase();

        if (normalizedStoredCode !== normalizedInputCode) {
          console.error("Invitation code mismatch:", {
            provided: normalizedInputCode,
            stored: normalizedStoredCode,
          });

          toast({
            title: "Sign up failed",
            description: "Invalid invitation code. Please check and try again.",
            variant: "destructive",
          });
          throw new Error("Invalid invitation code");
        }
      }

      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: userData.name,
            role: userData.role,
          },
          emailRedirectTo: window.location.origin,
        },
      });

      if (authError) {
        console.error("Signup error:", authError);
        toast({
          title: "Sign up failed",
          description: authError.message,
          variant: "destructive",
        });
        throw authError;
      }

      if (!authData.user) {
        throw new Error("User creation failed");
      }

      toast({
        title: "Sign up successful",
        description: "Your account has been created!",
      });

      // Set access level for admin users
      const accessLevel =
        userData.role === "admin"
          ? userData.accessLevel || 1 // Default to school admin (1)
          : null;

      // Create a new profile record
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .insert({
          id: authData.user.id,
          user_id: authData.user.id,
          role: userData.role,
          name: userData.name || "User",
          email: authData.user.email,
          student_id: userData.role === "student" ? `S-${Date.now()}` : null,
          teacher_id: userData.role === "teacher" ? `T-${Date.now()}` : null,
          admin_id: userData.role === "admin" ? `A-${Date.now()}` : null,
          school_id: userData.school_id || null,
          school: userData.school || null, // For backward compatibility
          access_level: accessLevel,
          preferred_language: userData.preferred_language || "en",
          profile_completed: false, // New users need to complete their profile setup
          is_blocked: false, // Explicitly set as not blocked
        })
        .select()
        .single();

      if (profileError) {
        console.error("Error creating profile:", profileError);
        // Still continue as we can retry profile creation later
      }

      // For immediate redirection to the appropriate dashboard
      const userProfile = {
        id: profileData?.id || authData.user.id,
        user_id: authData.user.id,
        name: userData.name || "User",
        email: authData.user.email || "",
        role: (userData.role as "student" | "teacher" | "admin") || "student",
        studentId: userData.role === "student" ? `S-${Date.now()}` : undefined,
        teacherId: userData.role === "teacher" ? `T-${Date.now()}` : undefined,
        adminId: userData.role === "admin" ? `A-${Date.now()}` : undefined,
        school_id: userData.school_id,
        school: userData.school,
        accessLevel:
          userData.role === "admin" ? userData.accessLevel || 1 : undefined,
      };

      setUser(authData.user);
      setProfile(userProfile);

      // Redirect to profile setup for student/teacher or directly to respective dashboard
      if (userData.role === "student") {
        navigate("/student?setup=true");
      } else if (userData.role === "teacher") {
        navigate("/teacher?setup=true");
      } else if (userData.role === "admin") {
        navigate("/admin-profile-setup?setup=true");
      }
    } catch (error: any) {
      console.error("Sign up error:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);

      // Clear local state first
      setUser(null);
      setProfile(null);
      setSession(null);

      // Clear biometric session data
      localStorage.removeItem('biometric_session_active');
      localStorage.removeItem('biometric_user_data');
      sessionStorage.removeItem('biometric_auth_user');
      sessionStorage.removeItem('biometric_auth_success');

      // Get current session
      const {
        data: { session: currentSession },
      } = await supabase.auth.getSession();

      // If there's no session, just redirect
      if (!currentSession) {
        navigate("/login");
        return;
      }

      // Attempt to sign out
      const { error } = await supabase.auth.signOut({
        scope: "local", // Change to local scope instead of global
      });

      if (error) {
        console.error("Sign out error:", error);
        toast({
          title: "Logout Error",
          description: "There was a problem logging out. Please try again.",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Logged out",
        description: "You have been successfully logged out",
      });

      navigate("/login");
    } catch (error: any) {
      console.error("Sign out error:", error);
      toast({
        title: "Logout Error",
        description: "There was a problem logging out. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (data: Partial<User>) => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please log in to update your profile",
        variant: "destructive",
      });
      return Promise.reject(new Error("Authentication required"));
    }

    try {
      setLoading(true);

      // Convert from our app's User type to Supabase format
      const updates = {
        name: data.name,
        photo_url: data.photoUrl,
        student_id: data.studentId,
        teacher_id: data.teacherId,
        admin_id: data.adminId,
        department: data.department,
        position: data.position,
        subject: data.subject,
        course: data.course,
        biometric_registered: data.biometricRegistered,
        block_id: data.block_id, // ✅ Added missing block_id
        room_id: data.room_id, // ✅ Added missing room_id
        block_name: data.blockName,
        room_number: data.roomNumber,
        pin: data.pin,
        school: data.school,
        school_id: data.school_id, // ✅ Added missing school_id
        profile_completed: true,
        updated_at: new Date().toISOString(),
      };

      // Submitting profile update to Supabase

      // Check if profile exists first
      const { data: existingProfile, error: fetchError } = await supabase
        .from("profiles")
        .select("id")
        .eq("user_id", user.id)
        .maybeSingle();

      if (fetchError && fetchError.code !== "PGRST116") {
        console.error("Error checking profile existence:", fetchError);
        throw fetchError;
      }

      let profileUpdateResult;

      if (existingProfile) {
        // Updating existing profile
        // Update existing profile
        profileUpdateResult = await supabase
          .from("profiles")
          .update(updates)
          .eq("user_id", user.id)
          .select();
      } else {
        // Creating new profile
        // Insert new profile if it doesn't exist
        profileUpdateResult = await supabase
          .from("profiles")
          .insert({
            ...updates,
            id: user.id,
            user_id: user.id,
            role: profile?.role || "student",
            email: profile?.email || user.email || "",
          })
          .select();
      }

      const { error: updateError, data: updatedData } = profileUpdateResult;

      if (updateError) {
        console.error("Profile update error:", updateError);
        throw updateError;
      }

      // Profile updated successfully

      // Refresh JWT claims
      const { error: refreshError } = await supabase.rpc("refresh_jwt_claims", {
        target_user_id: user.id,
      });

      if (refreshError) {
        console.error("Error refreshing JWT claims:", refreshError);
        // Don't throw, as the profile update was successful
      }

      // Update local state with the new profile data
      if (profile) {
        const updatedProfile = { ...profile, ...data };
        setProfile(updatedProfile);

        // Local profile state updated
      }

      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully",
      });

      // If this was a profile setup, redirect to dashboard
      const isProfileSetup = window.location.search.includes("setup=true");
      if (isProfileSetup) {
        const updatedProfile = { ...profile, ...data } as User;
        if (
          updatedProfile.role === "student" &&
          updatedProfile.course &&
          updatedProfile.block_id &&
          updatedProfile.room_id &&
          updatedProfile.pin &&
          updatedProfile.school_id
        ) {
          setTimeout(() => navigate("/student"), 500);
        } else if (
          updatedProfile.role === "teacher" &&
          updatedProfile.department &&
          updatedProfile.position &&
          updatedProfile.subject &&
          updatedProfile.school_id
        ) {
          setTimeout(() => navigate("/teacher"), 500);
        } else if (
          updatedProfile.role === "admin" &&
          updatedProfile.position &&
          updatedProfile.school
        ) {
          setTimeout(() => navigate("/admin"), 500);
        }
      }

      return Promise.resolve();
    } catch (error: any) {
      console.error("Profile update error:", error);
      toast({
        title: "Update failed",
        description:
          "There was an error updating your profile: " + error.message,
        variant: "destructive",
      });
      return Promise.reject(error);
    } finally {
      setLoading(false);
    }
  };

  const signInWithBiometrics = async (email: string) => {
    try {
      setLoading(true);

      // Check if WebAuthn is available
      if (!navigator.credentials || !window.PublicKeyCredential) {
        throw new Error('Biometric authentication is not supported on this device or browser');
      }

      // Get user profile using RPC to bypass RLS
      const { data: profileData, error: profileError } = await supabase.rpc('get_user_for_biometric_auth', {
        user_email: email
      });

      if (profileError) {
        throw new Error('Failed to fetch user profile');
      }

      if (!profileData) {
        throw new Error('User not found');
      }

      if (!profileData.biometric_registered) {
        throw new Error('Biometric authentication not set up for this account');
      }

      // Import WebAuthn functions
      const { startAuthentication } = await import('@/lib/webauthn');

      // Perform WebAuthn authentication
      await startAuthentication(profileData.user_id);

      // Biometric authentication successful!
      // Create a direct session by simulating the auth flow

      // Store verification data for session creation
      const verificationData = {
        user_id: profileData.user_id,
        email: email,
        name: profileData.name,
        role: profileData.role,
        timestamp: Date.now(),
        biometric_verified: true
      };

      // For demo purposes, we'll use a direct approach
      // In production, you'd have a secure server endpoint that creates the session

      try {
        // Method 1: Try to use the existing auth system with a special token
        const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
          email: email,
          password: 'biometric_auth_bypass' // This won't work but we'll catch it
        });

        if (authError) {
          // Expected to fail, so we'll use our fallback method
          throw new Error('Using fallback method');
        }
      } catch (authError) {
        // Fallback: Create a temporary session state
        // Store the user data temporarily and redirect
        sessionStorage.setItem('biometric_auth_user', JSON.stringify(verificationData));
        sessionStorage.setItem('biometric_auth_success', 'true');

        toast({
          title: "✨ Biometric sign-in successful!",
          description: "Welcome back! Redirecting to dashboard...",
        });

        // Set a flag to indicate successful biometric auth
        localStorage.setItem('biometric_session_active', 'true');
        localStorage.setItem('biometric_user_data', JSON.stringify(verificationData));

        // Determine the correct dashboard URL based on user role
        let dashboardUrl = '/student'; // default
        if (profileData.role === 'teacher') {
          dashboardUrl = '/teacher';
        } else if (profileData.role === 'admin') {
          dashboardUrl = '/admin';
        }

        // Redirect to appropriate dashboard immediately
        setTimeout(() => {
          window.location.href = dashboardUrl;
        }, 1000);

        return;
      }

    } catch (error: any) {
      console.error("Biometric sign-in error:", error);

      let errorMessage = "Biometric authentication failed";
      if (error.message.includes('not found')) {
        errorMessage = "User not found. Please check your email address.";
      } else if (error.message.includes('not registered')) {
        errorMessage = "Biometric authentication not set up. Please use email/password or register biometrics first.";
      } else if (error.message.includes('not supported')) {
        errorMessage = "Biometric authentication not supported on this device.";
      } else if (error.message.includes('cancelled') || error.message.includes('User cancelled')) {
        errorMessage = "Biometric authentication was cancelled.";
      } else if (error.message.includes('timeout')) {
        errorMessage = "Biometric authentication timed out. Please try again.";
      }

      toast({
        title: "Biometric sign-in failed",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    profile,
    session,
    loading,
    signIn,
    signInWithBiometrics,
    signUp,
    signOut,
    updateProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }

  return context;
};
