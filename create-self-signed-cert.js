#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import os from 'os';

console.log('🔐 Creating self-signed SSL certificate for HTTPS development...\n');

// Create certs directory if it doesn't exist
const certsDir = path.join(process.cwd(), 'certs');
if (!fs.existsSync(certsDir)) {
  fs.mkdirSync(certsDir);
  console.log('📁 Created certs directory');
}

// Get local IP address
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return 'localhost';
}

const localIP = getLocalIP();

// Create OpenSSL config file
const configContent = `
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = Development
L = Local
O = Attendance Tracking System
CN = localhost

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
DNS.3 = *.local
IP.1 = 127.0.0.1
IP.2 = ::1
IP.3 = ${localIP}
`;

const configPath = path.join(certsDir, 'openssl.conf');
fs.writeFileSync(configPath, configContent);

try {
  console.log('🔧 Generating self-signed SSL certificate...');
  
  // Generate private key
  execSync(`openssl genrsa -out "${path.join(certsDir, 'localhost-key.pem')}" 2048`, { stdio: 'pipe' });
  console.log('✅ Private key generated');
  
  // Generate certificate
  execSync(`openssl req -new -x509 -key "${path.join(certsDir, 'localhost-key.pem')}" -out "${path.join(certsDir, 'localhost.pem')}" -days 365 -config "${configPath}"`, { stdio: 'pipe' });
  console.log('✅ Certificate generated');
  
  // Clean up config file
  fs.unlinkSync(configPath);
  
  console.log('\n🎉 SSL certificate created successfully!');
  console.log('\n📋 Next steps:');
  console.log('1. Start the development server: npm run dev');
  console.log(`2. Access your app on your phone: https://${localIP}:5173`);
  console.log('3. Accept the security warning on your phone (self-signed certificate)');
  console.log('4. Test biometric authentication!');
  console.log('\n⚠️  Note: You will see a security warning because this is a self-signed certificate.');
  console.log('   This is normal for development. Click "Advanced" and "Proceed" to continue.');
  
} catch (error) {
  console.error('❌ Error generating certificate:', error.message);
  console.log('\n📋 Alternative solutions:');
  console.log('1. Install OpenSSL: https://slproweb.com/products/Win32OpenSSL.html');
  console.log('2. Use Git Bash (which includes OpenSSL)');
  console.log('3. Install mkcert manually: https://github.com/FiloSottile/mkcert/releases');
  console.log('4. Use the vite-plugin-mkcert (may require manual certificate trust)');
}
