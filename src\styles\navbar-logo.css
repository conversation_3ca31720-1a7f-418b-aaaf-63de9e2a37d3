/* Custom styles for the school logo in the navbar */

.school-logo-container {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.school-logo-container:hover {
  transform: scale(1.05);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.school-logo {
  object-fit: contain;
  transition: all 0.3s ease;
}

.school-logo-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  text-transform: uppercase;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.2) 100%
  );
}

/* Pulse animation for the logo */
@keyframes pulse-border {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

.pulse-animation {
  animation: pulse-border 2s infinite;
}

/* User profile dropdown styles */
.user-profile-trigger {
  position: relative;
  transition: all 0.2s ease;
}

.user-profile-trigger:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-profile-avatar {
  border-radius: 50%;
  overflow: hidden;
}

/* Dropdown menu item hover effect */
.dropdown-menu-item {
  transition: all 0.2s ease;
  border-radius: 4px;
}

.dropdown-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Logout button specific styles */
.logout-button {
  color: #ff3366;
}

.logout-button:hover {
  background-color: rgba(255, 51, 102, 0.1);
}
