-- =====================================================
-- ATTENDANCE RECORDS FOREIGN KEY FIX
-- =====================================================
-- Run this if you're getting foreign key relationship errors

-- Check current attendance_records table structure
-- \d attendance_records;

-- Add foreign key constraints if they don't exist
-- This ensures proper relationships for Supabase queries

-- Add foreign key to profiles table (student_id -> profiles.id)
DO $$ 
BEGIN
    -- Check if foreign key constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'attendance_records_student_id_fkey' 
        AND table_name = 'attendance_records'
    ) THEN
        -- Add the foreign key constraint
        ALTER TABLE attendance_records 
        ADD CONSTRAINT attendance_records_student_id_fkey 
        FOREIGN KEY (student_id) REFERENCES profiles(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Add foreign key to rooms table (room_id -> rooms.id)
DO $$ 
BEGIN
    -- Check if foreign key constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'attendance_records_room_id_fkey' 
        AND table_name = 'attendance_records'
    ) THEN
        -- Add the foreign key constraint
        ALTER TABLE attendance_records 
        ADD CONSTRAINT attendance_records_room_id_fkey 
        FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE SET NULL;
    END IF;
END $$;

-- Add foreign key to schools table (school_id -> schools.id)
DO $$ 
BEGIN
    -- Check if foreign key constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'attendance_records_school_id_fkey' 
        AND table_name = 'attendance_records'
    ) THEN
        -- Add the foreign key constraint
        ALTER TABLE attendance_records 
        ADD CONSTRAINT attendance_records_school_id_fkey 
        FOREIGN KEY (school_id) REFERENCES schools(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Verify the foreign keys were created
SELECT 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name='attendance_records';
