import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import Navbar from "@/components/shared/Navbar";
import {
  Profile as StudentProfile,
  Notifications as StudentNotifications,
  Excuses as StudentExcuses,
} from "@/components/student";
import SocialMediaFeed from "@/components/student/SocialMediaFeed";
import EnhancedAttendanceHistory from "@/components/student/EnhancedAttendanceHistory";
import PageTransition from "@/components/student/PageTransition";
import ProductionQRScanner from "@/components/student/ProductionQRScanner";
import EnhancedTabNavigation from "@/components/student/EnhancedTabNavigation";
import { Student as StudentType } from "@/lib/types";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { motion, AnimatePresence } from "framer-motion";
import { TabProvider } from "@/context/TabContext";
import { useTranslation } from "react-i18next";
import DashboardMessage from "@/components/shared/DashboardMessage";
import Footer from "@/components/shared/Footer";
import FeedbackForm from "@/components/shared/FeedbackForm";
import SimpleCarousel from "@/components/shared/SimpleCarousel";

export default function Student() {
  const { profile, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation(); // Add translation hook
  const isProfileSetup =
    new URLSearchParams(location.search).get("setup") === "true";
  const [activeTab, setActiveTab] = useState<string>("scan");

  // Expose setActiveTab globally for direct access
  // @ts-ignore
  window.setStudentActiveTab = setActiveTab;
  const [unreadCount, setUnreadCount] = useState(0);

  // Helper function to check if student needs profile setup
  const needsProfileSetup = (studentProfile: StudentType) => {
    return (
      !studentProfile.course ||
      !studentProfile.block_id ||
      !studentProfile.room_id ||
      !studentProfile.pin ||
      !studentProfile.school_id
    );
  };

  // Fetch unread notifications count
  useEffect(() => {
    if (!profile?.id) return;

    const fetchUnreadCount = async () => {
      const { count, error } = await supabase
        .from("notifications")
        .select("*", { count: "exact", head: true })
        .eq("student_id", profile.id)
        .eq("read", false);

      if (!error && count !== null) {
        setUnreadCount(count);

        // Store the viewed status in localStorage when the count is 0
        if (count === 0) {
          localStorage.setItem("notificationsViewed", "true");
        }
      }
    };

    // Check if the notifications tab is active
    if (activeTab === "notifications") {
      // If the user is viewing the notifications tab, mark as viewed
      localStorage.setItem("notificationsViewed", "true");
    }

    fetchUnreadCount();

    // Subscribe to notification changes
    const channel = supabase
      .channel("student-notification-count")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "notifications",
          filter: `student_id=eq.${profile.id}::uuid AND read=eq.false`,
        },
        (payload) => {
          if (payload.eventType === "INSERT") {
            // New notification received, increment count and mark as unviewed
            setUnreadCount((prev) => prev + 1);
            localStorage.removeItem("notificationsViewed");
          } else if (
            payload.eventType === "UPDATE" ||
            payload.eventType === "DELETE"
          ) {
            // Notification updated or deleted, refresh count
            fetchUnreadCount();
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [profile?.id, activeTab]);

  useEffect(() => {
    // If we're in setup mode or profile is incomplete, force the profile tab
    if (
      isProfileSetup ||
      (profile &&
        profile.role === "student" &&
        needsProfileSetup(profile as StudentType))
    ) {
      setActiveTab("profile");
    }
  }, [isProfileSetup, profile]);

  // Reset unread count when opening notifications tab
  useEffect(() => {
    if (activeTab === "notifications") {
      setUnreadCount(0);
    }
  }, [activeTab]);

  // If loading, show loading indicator
  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  // If not a student, redirect to login
  if (!profile || profile.role !== "student") {
    navigate("/login");
    return null;
  }

  const studentProfile = profile as StudentType;

  // Check if this is a new student that needs to complete profile
  const requiresProfileSetup =
    isProfileSetup || needsProfileSetup(studentProfile);

  // Force redirect to profile setup if needed (for fresh signups/logins)
  useEffect(() => {
    if (requiresProfileSetup && !isProfileSetup) {
      navigate("/student?setup=true");
    }
  }, [requiresProfileSetup, isProfileSetup, navigate]);

  // Set active tab based on URL parameter
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const tabParam = params.get("tab");
    if (
      tabParam &&
      ["scan", "history", "excuses", "profile", "notifications"].includes(
        tabParam
      )
    ) {
      setActiveTab(tabParam);
    }
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      {requiresProfileSetup ? (
        <div className="container mx-auto py-6 px-4 flex-1">
          {/* Render the Profile component for setup - it has its own title and message */}
          <StudentProfile isSetupMode={true} />
        </div>
      ) : (
        <div className="flex-1">
          {/* Dashboard Carousel with title overlay - directly below navbar */}
          <SimpleCarousel userType="student" />

          {/* Display dashboard message below carousel */}
          <div className="container mx-auto px-4 mt-6">
            <DashboardMessage userType="student" />

            {/* Floating feedback button */}
            <FeedbackForm variant="fab" />

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <EnhancedTabNavigation
                activeTab={activeTab}
                setActiveTab={setActiveTab}
                unreadCount={unreadCount}
                requiresProfileSetup={requiresProfileSetup}
              />

              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <TabsContent value="scan" className="mt-0">
                    <div className="space-y-6">
                      <ProductionQRScanner />
                      <SocialMediaFeed />
                    </div>
                  </TabsContent>

                  <TabsContent value="history" className="mt-0">
                    <EnhancedAttendanceHistory />
                  </TabsContent>

                  <TabsContent value="excuses" className="mt-0">
                    <StudentExcuses />
                  </TabsContent>

                  <TabsContent value="profile" className="mt-0">
                    <StudentProfile isSetupMode={requiresProfileSetup} />
                  </TabsContent>

                  <TabsContent value="notifications" className="mt-0">
                    <StudentNotifications />
                  </TabsContent>


                </motion.div>
              </AnimatePresence>
            </Tabs>
          </div>
        </div>
      )}
      {!requiresProfileSetup && <Footer />}
    </div>
  );
}
