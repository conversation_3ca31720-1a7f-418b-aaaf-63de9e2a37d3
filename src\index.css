/* Import custom styles first */
@import "./styles/phone-input.css";
@import "./styles/dark-mode.css";
@import "./styles/force-orange.css";
@import "./styles/light-mode.css";
@import "./styles/navbar-logo.css";
@import "./styles/icon-fix.css"; /* Import the icon fix CSS */
@import "./styles/enhanced-colors.css"; /* Import enhanced color system */
@import "./styles/enhanced-animations.css"; /* Import enhanced animations */
@import "./styles/teacher-card-enhancements.css"; /* Import teacher card enhancements */
@import "./styles/scrollbar-hide.css"; /* Import scrollbar hiding utility */
@import "./styles/custom-camera-icon.css"; /* Import custom camera icon styles */
@import "./styles/custom-scrollbar.css"; /* Import custom scrollbar styles */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Force CSS refresh when theme changes */
.theme-refreshing * {
  transition: none !important;
}

/* Theme refreshing animation */
.theme-refreshing {
  transition: all 0.2s ease-in-out;
}

@layer base {
  :root {
    /* Light mode - Clean, professional colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Light mode primary - Logo navy blue */
    --primary: 225 80% 16%;
    --primary-foreground: 0 0% 100%;

    /* Light mode secondary - Light gray */
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 217.2 91.2% 59.8%;

    --radius: 0.5rem;

    /* Light mode sidebar */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark Gray/Black background from resume website */
    --background: 220 7% 13%;
    --foreground: 220 14% 80%;

    --card: 220 7% 15%;
    --card-foreground: 220 14% 80%;

    --popover: 220 7% 13%;
    --popover-foreground: 220 14% 80%;

    /* Vibrant Orange for primary elements */
    --primary: 32 89% 56%;
    --primary-foreground: 0 0% 100%;

    /* Direct color override for navbar - vibrant orange */
    --navbar-bg: #f39228;

    /* Slightly darker shade for secondary elements */
    --secondary: 220 7% 20%;
    --secondary-foreground: 220 14% 80%;

    --muted: 220 7% 20%;
    --muted-foreground: 220 10% 70%;

    --accent: 220 7% 20%;
    --accent-foreground: 220 14% 80%;

    /* Orange accent for destructive elements */
    --destructive: 16 85% 55%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 7% 20%;
    --input: 220 7% 20%;
    --ring: 32 89% 56%;

    /* Sidebar styling */
    --sidebar-background: 220 7% 10%;
    --sidebar-foreground: 220 14% 80%;
    --sidebar-primary: 32 89% 56%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 7% 17%;
    --sidebar-accent-foreground: 220 14% 80%;
    --sidebar-border: 220 7% 17%;
    --sidebar-ring: 32 89% 56%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}
