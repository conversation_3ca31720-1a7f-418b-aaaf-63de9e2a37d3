import { ReactNode } from "react";

interface EnhancedStatsCardProps {
  title: string;
  value: number;
  total: number;
  icon: ReactNode;
  description: string;
  colorClass: string;
}

export function EnhancedStatsCard({ 
  title, 
  value, 
  total, 
  icon, 
  description,
  colorClass
}: EnhancedStatsCardProps) {
  const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
  
  // Determine color classes based on the colorClass prop
  let bgColor = "bg-primary/10";
  let textColor = "text-primary";
  let progressBgColor = "bg-primary/20";
  let progressColor = "bg-primary";
  
  if (colorClass === "green") {
    bgColor = "bg-green-100 dark:bg-green-900/30";
    textColor = "text-green-600 dark:text-green-400";
    progressBgColor = "bg-green-100 dark:bg-green-900/30";
    progressColor = "bg-green-500 dark:bg-green-400";
  } else if (colorClass === "red") {
    bgColor = "bg-red-100 dark:bg-red-900/30";
    textColor = "text-red-600 dark:text-red-400";
    progressBgColor = "bg-red-100 dark:bg-red-900/30";
    progressColor = "bg-red-500 dark:bg-red-400";
  } else if (colorClass === "amber") {
    bgColor = "bg-amber-100 dark:bg-amber-900/30";
    textColor = "text-amber-600 dark:text-amber-400";
    progressBgColor = "bg-amber-100 dark:bg-amber-900/30";
    progressColor = "bg-amber-500 dark:bg-amber-400";
  } else if (colorClass === "blue") {
    bgColor = "bg-blue-100 dark:bg-blue-900/30";
    textColor = "text-blue-600 dark:text-blue-400";
    progressBgColor = "bg-blue-100 dark:bg-blue-900/30";
    progressColor = "bg-blue-500 dark:bg-blue-400";
  }

  return (
    <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6 transition-all duration-300 hover:shadow-md hover:-translate-y-1">
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full ${bgColor}`}>
              <div className={textColor}>{icon}</div>
            </div>
            <div>
              <p className="text-sm font-medium leading-none">{title}</p>
              <p className="text-xs text-muted-foreground mt-1">{description}</p>
            </div>
          </div>
          <div className="text-right">
            <div className={`text-2xl font-bold ${textColor}`}>{value}</div>
            <p className="text-xs text-muted-foreground">
              {percentage}% of total
            </p>
          </div>
        </div>
        
        <div className="w-full">
          <div className={`w-full h-2 ${progressBgColor} rounded-full overflow-hidden`}>
            <div 
              className={`h-full ${progressColor} rounded-full transition-all duration-500 ease-out`}
              style={{ width: `${percentage}%` }}
            />
          </div>
          <div className="flex justify-between mt-1">
            <span className="text-xs text-muted-foreground">{value}/{total}</span>
            <span className="text-xs text-muted-foreground">{percentage}%</span>
          </div>
        </div>
      </div>
    </div>
  );
}
