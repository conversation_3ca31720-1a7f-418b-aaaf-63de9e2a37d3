# Deploying Supabase Edge Functions

To enable real email and SMS sending, you need to deploy the Supabase Edge Function we've created. Follow these steps:

## 1. Install the Supabase CLI

If you haven't already installed the Supabase CLI, you can do so with:

```bash
npm install -g supabase
```

## 2. Log in to Supabase

```bash
supabase login
```

This will open a browser window where you can log in to your Supabase account and generate an access token.

## 3. Deploy the Edge Function

```bash
supabase functions deploy send-notification --project-ref wclwxrilybnzkhvqzbmy
```

## 4. Set Environment Variables

The Edge Function needs access to your Supabase URL and anon key. Set these environment variables:

```bash
supabase secrets set SUPABASE_URL=https://wclwxrilybnzkhvqzbmy.supabase.co --project-ref wclwxrilybnzkhvqzbmy
supabase secrets set SUPABASE_ANON_KEY=your-anon-key-here --project-ref wclwxrilybnzkhvqzbmy
```

Replace `your-anon-key-here` with your actual Supabase anon key.

## 5. Test the Function

After deploying, you can test the function by:

1. Configuring your email and SMS settings in the "Email & Services" tab
2. Sending a test email or SMS

## Troubleshooting

If you encounter any issues:

1. Check the Supabase dashboard for function logs
2. Make sure your SendGrid and Twilio credentials are correct
3. Verify that your Supabase project has the correct permissions

## Alternative: Simulated Mode

If you prefer not to deploy the Edge Function, you can revert to the simulated mode by:

1. Editing `src/lib/services/external-notification-services.ts`
2. Replacing the real implementation with the simulated one

This will allow you to test the functionality without actually sending emails or SMS messages.
