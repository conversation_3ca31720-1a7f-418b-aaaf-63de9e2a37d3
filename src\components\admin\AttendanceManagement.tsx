import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { FileText, Building2, Calendar, Download, Bell } from "lucide-react";
import { useTranslation } from "react-i18next";
import AttendanceReports from "./attendance-management/AttendanceReports";
import BlockRoomManagement from "./attendance-management/BlockRoomManagement";
import AttendanceReminders from "./attendance-management/AttendanceReminders";

export default function AttendanceManagement() {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("reports");

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-2">
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">
          {t("admin.attendanceManagement.title")}
        </h1>
        <p className="text-muted-foreground text-sm sm:text-base">
          {t("admin.attendanceManagement.description")}
        </p>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 lg:w-[600px]">
          <TabsTrigger value="reports" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span className="hidden sm:inline">
              {t("admin.attendanceManagement.reports")}
            </span>
            <span className="sm:hidden">
              {t("admin.attendanceManagement.reportsShort")}
            </span>
          </TabsTrigger>
          <TabsTrigger value="reminders" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            <span className="hidden sm:inline">
              {t("admin.attendanceManagement.remindersTab")}
            </span>
            <span className="sm:hidden">
              {t("admin.attendanceManagement.remindersShort")}
            </span>
          </TabsTrigger>
          <TabsTrigger value="structure" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            <span className="hidden sm:inline">
              {t("admin.attendanceManagement.structure")}
            </span>
            <span className="sm:hidden">
              {t("admin.attendanceManagement.structureShort")}
            </span>
          </TabsTrigger>
        </TabsList>

        {/* Attendance Reports Tab */}
        <TabsContent value="reports" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                {t("admin.attendanceManagement.dailyReports")}
              </CardTitle>
              <CardDescription>
                {t("admin.attendanceManagement.dailyReportsDescription")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AttendanceReports />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Attendance Reminders Tab */}
        <TabsContent value="reminders" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                {t("admin.attendanceManagement.reminders.title")}
              </CardTitle>
              <CardDescription>
                {t("admin.attendanceManagement.reminders.description")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AttendanceReminders />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Block & Room Management Tab */}
        <TabsContent value="structure" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                {t("admin.attendanceManagement.schoolStructure")}
              </CardTitle>
              <CardDescription>
                {t("admin.attendanceManagement.schoolStructureDescription")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BlockRoomManagement />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
