import { createClient } from "@supabase/supabase-js";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: Supabase URL or service key not found in environment variables"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration() {
  try {
    console.log("Starting notification migration...");

    // Read the migration file
    const migrationPath = path.join(
      __dirname,
      "../supabase/migrations/20240514000000_fix_notifications.sql"
    );
    const migrationSql = fs.readFileSync(migrationPath, "utf8");

    // Split the SQL into individual statements
    const statements = migrationSql
      .split(";")
      .map((statement) => statement.trim())
      .filter((statement) => statement.length > 0);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`Executing statement ${i + 1} of ${statements.length}...`);

      try {
        const { data, error } = await supabase.rpc("execute_sql", {
          sql: statement + ";",
        });

        if (error) {
          console.error(`Error executing statement ${i + 1}:`, error);
        } else {
          console.log(`Statement ${i + 1} executed successfully`);
        }
      } catch (err) {
        console.error(`Error executing statement ${i + 1}:`, err);
      }
    }

    console.log("Notification migration completed");

    // Test creating a notification
    console.log("Testing notification creation...");

    // Get a student ID for testing
    const { data: students, error: studentError } = await supabase
      .from("profiles")
      .select("id")
      .eq("role", "student")
      .limit(1);

    if (studentError) {
      console.error("Error fetching student for testing:", studentError);
    } else if (students && students.length > 0) {
      const studentId = students[0].id;

      // Create a test notification
      const { data: notification, error: notificationError } = await supabase
        .from("notifications")
        .insert({
          student_id: studentId,
          title: "🧪 Test Notification",
          message:
            "This is a test notification to verify the migration worked correctly.",
          type: "system",
          read: false,
          timestamp: new Date().toISOString(),
          metadata: {
            test: true,
            created_by: "migration_script",
          },
        })
        .select();

      if (notificationError) {
        console.error("Error creating test notification:", notificationError);
      } else {
        console.log("Test notification created successfully:", notification);
      }
    }
  } catch (error) {
    console.error("Error applying migration:", error);
    process.exit(1);
  }
}

// Execute the migration
runMigration();
