-- Create a function to get detailed profile information
CREATE OR REPLACE FUNCTION public.debug_profile_state(target_user_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result jsonb;
  profile_data jsonb;
  auth_data jsonb;
BEGIN
  -- Get profile data
  SELECT row_to_json(p)::jsonb INTO profile_data
  FROM profiles p
  WHERE user_id = target_user_id;
  
  -- Get auth user data
  SELECT row_to_json(u)::jsonb INTO auth_data
  FROM auth.users u
  WHERE id = target_user_id;
  
  -- Build result
  result = jsonb_build_object(
    'profile', profile_data,
    'auth_user', auth_data,
    'jwt_claims', auth.jwt(),
    'current_user', auth.uid(),
    'has_profile_access', EXISTS (
      SELECT 1 FROM profiles 
      WHERE user_id = target_user_id 
      AND (
        user_id = auth.uid() 
        OR 
        EXISTS (
          SELECT 1 FROM profiles 
          WHERE user_id = auth.uid() 
          AND role = 'admin'
        )
      )
    )
  );
  
  RETURN result;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.debug_profile_state(uuid) TO authenticated; 