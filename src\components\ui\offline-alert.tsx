import { useState, useEffect } from "react";
import { Wifi, WifiOff, Refresh<PERSON>w, ExternalLink } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { useOnlineStatus } from "@/hooks/useOnlineStatus";
import { useTranslation } from "react-i18next";

interface OfflineAlertProps {
  className?: string;
}

export function OfflineAlert({ className }: OfflineAlertProps) {
  const { isOnline, checkConnection, goToOfflinePage } = useOnlineStatus();
  const { t } = useTranslation();
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [showRetryAnimation, setShowRetryAnimation] = useState<boolean>(false);

  useEffect(() => {
    // Update visibility based on online status
    if (isOnline) {
      // Keep the alert visible for a moment when coming back online
      setTimeout(() => setIsVisible(false), 3000);
    } else {
      setIsVisible(true);
    }
  }, [isOnline]);

  // Check initial status
  useEffect(() => {
    setIsVisible(!navigator.onLine);

    // Periodically check connection status
    const intervalId = setInterval(() => {
      checkConnection();
    }, 30000); // Check every 30 seconds

    return () => clearInterval(intervalId);
  }, [checkConnection]);

  const handleRetry = async () => {
    setShowRetryAnimation(true);

    // Try to reload the page or reconnect
    if (isOnline) {
      window.location.reload();
    } else {
      // Try to check connection
      const isConnected = await checkConnection();

      if (isConnected) {
        window.location.reload();
      } else {
        // Just show animation for visual feedback when offline
        setTimeout(() => setShowRetryAnimation(false), 1500);
      }
    }
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        transition={{ duration: 0.3 }}
        className={`fixed top-2 sm:top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-xs sm:max-w-md mx-2 sm:mx-0 ${className}`}
      >
        <Alert
          className={`shadow-xl border-2 backdrop-blur-sm ${
            isOnline
              ? "bg-green-50/95 border-green-200"
              : "bg-amber-50/95 border-amber-200"
          }`}
        >
          <div className="flex items-start gap-2 sm:gap-3">
            {isOnline ? (
              <Wifi className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mt-0.5 flex-shrink-0" />
            ) : (
              <WifiOff className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600 mt-0.5 flex-shrink-0" />
            )}
            <div className="flex-1 min-w-0">
              <AlertTitle
                className={`text-sm sm:text-base font-semibold ${isOnline ? "text-green-800" : "text-amber-800"}`}
              >
                {isOnline ? t("offline.connectionRestored") : t("offline.title")}
              </AlertTitle>
              <AlertDescription
                className={`text-xs sm:text-sm mt-1 ${isOnline ? "text-green-700" : "text-amber-700"}`}
              >
                {isOnline
                  ? t("offline.connectionRestoredDescription")
                  : t("offline.offlineDescription")}
              </AlertDescription>
            </div>
            <div className="flex flex-col gap-1.5 sm:gap-2 flex-shrink-0">
              <Button
                size="sm"
                variant="outline"
                className={`text-xs px-2 py-1 h-auto ${
                  isOnline
                    ? "border-green-200 text-green-700 hover:bg-green-100"
                    : "border-amber-200 text-amber-700 hover:bg-amber-100"
                }`}
                onClick={handleRetry}
              >
                <RefreshCw
                  className={`h-3 w-3 mr-1 ${
                    showRetryAnimation ? "animate-spin" : ""
                  }`}
                />
                <span className="hidden sm:inline">
                  {isOnline ? t("offline.refresh") : t("offline.retry")}
                </span>
                <span className="sm:hidden">
                  {isOnline ? "↻" : "⟲"}
                </span>
              </Button>

              {!isOnline && (
                <Button
                  size="sm"
                  variant="outline"
                  className="border-amber-200 text-amber-700 hover:bg-amber-100 text-xs px-2 py-1 h-auto"
                  onClick={goToOfflinePage}
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  <span className="hidden sm:inline">
                    {t("offline.offlineMode")}
                  </span>
                  <span className="sm:hidden">
                    ⚡
                  </span>
                </Button>
              )}
            </div>
          </div>
        </Alert>
      </motion.div>
    </AnimatePresence>
  );
}
